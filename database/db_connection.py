#!/usr/bin/env python3.12
"""
Database Connection Manager for PinnacleAi Resource Manager
Handles PostgreSQL connections with connection pooling
"""

import os
import sys
import psycopg2
import psycopg2.pool
import threading
import time
from contextlib import contextmanager
from typing import Optional, Dict, Any, List, Tuple
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class DatabaseConnection:
    """Manages PostgreSQL database connections with pooling"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize database connection manager"""
        self.config = config or self._get_default_config()
        self.connection_pool = None
        self.lock = threading.Lock()
        self._setup_logging()
        
    def _setup_logging(self):
        """Setup logging for database operations"""
        self.logger = logging.getLogger('database')
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default database configuration"""
        return {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'resource_monitor'),
            'user': os.getenv('DB_USER', 'resource_user'),
            'password': os.getenv('DB_PASSWORD', 'pinnacle_resource_2025'),
            'minconn': int(os.getenv('DB_MIN_CONN', 2)),
            'maxconn': int(os.getenv('DB_MAX_CONN', 10)),
            'connect_timeout': int(os.getenv('DB_TIMEOUT', 30))
        }
    
    def initialize_pool(self) -> bool:
        """Initialize connection pool"""
        try:
            with self.lock:
                if self.connection_pool is None:
                    self.connection_pool = psycopg2.pool.ThreadedConnectionPool(
                        minconn=self.config['minconn'],
                        maxconn=self.config['maxconn'],
                        host=self.config['host'],
                        port=self.config['port'],
                        database=self.config['database'],
                        user=self.config['user'],
                        password=self.config['password'],
                        connect_timeout=self.config['connect_timeout']
                    )
                    self.logger.info("✅ Database connection pool initialized")
                    return True
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize connection pool: {e}")
            return False
        return True
    
    @contextmanager
    def get_connection(self):
        """Get database connection from pool (context manager)"""
        connection = None
        try:
            if self.connection_pool is None:
                if not self.initialize_pool():
                    raise Exception("Failed to initialize connection pool")
            
            connection = self.connection_pool.getconn()
            if connection:
                yield connection
            else:
                raise Exception("Failed to get connection from pool")
                
        except Exception as e:
            self.logger.error(f"Database connection error: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection and self.connection_pool:
                self.connection_pool.putconn(connection)
    
    def execute_query(self, query: str, params: Tuple = None) -> Optional[List[Tuple]]:
        """Execute a SELECT query and return results"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, params)
                    if cursor.description:  # SELECT query
                        return cursor.fetchall()
                    return None
        except Exception as e:
            self.logger.error(f"Query execution error: {e}")
            self.logger.error(f"Query: {query}")
            self.logger.error(f"Params: {params}")
            return None
    
    def execute_insert(self, query: str, params: Tuple = None) -> bool:
        """Execute an INSERT/UPDATE/DELETE query"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, params)
                    conn.commit()
                    return True
        except Exception as e:
            self.logger.error(f"Insert execution error: {e}")
            self.logger.error(f"Query: {query}")
            self.logger.error(f"Params: {params}")
            return False
    
    def execute_batch_insert(self, query: str, params_list: List[Tuple]) -> bool:
        """Execute batch INSERT operations"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.executemany(query, params_list)
                    conn.commit()
                    return True
        except Exception as e:
            self.logger.error(f"Batch insert error: {e}")
            self.logger.error(f"Query: {query}")
            return False
    
    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    if result and result[0] == 1:
                        self.logger.info("✅ Database connection test successful")
                        return True
            return False
        except Exception as e:
            self.logger.error(f"❌ Database connection test failed: {e}")
            return False
    
    def get_server_info(self) -> Dict[str, Any]:
        """Get database server information"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT version()")
                    version = cursor.fetchone()[0]
                    
                    cursor.execute("SELECT current_database()")
                    database = cursor.fetchone()[0]
                    
                    cursor.execute("SELECT current_user")
                    user = cursor.fetchone()[0]
                    
                    return {
                        'version': version,
                        'database': database,
                        'user': user,
                        'host': self.config['host'],
                        'port': self.config['port']
                    }
        except Exception as e:
            self.logger.error(f"Failed to get server info: {e}")
            return {}
    
    def close_pool(self):
        """Close connection pool"""
        try:
            with self.lock:
                if self.connection_pool:
                    self.connection_pool.closeall()
                    self.connection_pool = None
                    self.logger.info("✅ Database connection pool closed")
        except Exception as e:
            self.logger.error(f"Error closing connection pool: {e}")
    
    def __del__(self):
        """Cleanup on object destruction"""
        self.close_pool()


# Global database instance
_db_instance = None
_db_lock = threading.Lock()

def get_database() -> DatabaseConnection:
    """Get global database instance (singleton pattern)"""
    global _db_instance
    
    if _db_instance is None:
        with _db_lock:
            if _db_instance is None:
                _db_instance = DatabaseConnection()
                _db_instance.initialize_pool()
    
    return _db_instance

def test_database_connection() -> bool:
    """Test database connection"""
    try:
        db = get_database()
        return db.test_connection()
    except Exception as e:
        print(f"Database connection test failed: {e}")
        return False

if __name__ == "__main__":
    # Test the database connection
    print("🔍 Testing database connection...")
    
    db = DatabaseConnection()
    
    if db.test_connection():
        print("✅ Database connection successful!")
        
        # Get server info
        info = db.get_server_info()
        if info:
            print(f"📊 Database Info:")
            print(f"   Version: {info.get('version', 'Unknown')}")
            print(f"   Database: {info.get('database', 'Unknown')}")
            print(f"   User: {info.get('user', 'Unknown')}")
            print(f"   Host: {info.get('host', 'Unknown')}")
            print(f"   Port: {info.get('port', 'Unknown')}")
    else:
        print("❌ Database connection failed!")
        sys.exit(1)