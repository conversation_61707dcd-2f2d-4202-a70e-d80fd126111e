-- =====================================================
-- PinnacleAi RHEL Resource Manager Database Setup
-- Multi-Server Monitoring Database Schema
-- =====================================================

-- Create database
CREATE DATABASE resource_monitor;

-- Create user for the application
CREATE USER resource_user WITH PASSWORD 'pinnacle_resource_2025';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE resource_monitor TO resource_user;

-- Connect to the new database
\c resource_monitor;

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO resource_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO resource_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO resource_user;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO resource_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO resource_user;

-- =====================================================
-- TABLE 1: SERVERS REGISTRY
-- =====================================================
CREATE TABLE servers (
    id SERIAL PRIMARY KEY,
    server_ip VARCHAR(15) UNIQUE NOT NULL,
    hostname VARCHAR(255),
    server_name VARCHAR(255),
    os_info VARCHAR(255),
    cpu_cores INTEGER,
    total_memory BIGINT,
    status VARCHAR(20) DEFAULT 'active',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    location VARCHAR(255),
    environment VARCHAR(50), -- prod, staging, dev
    server_group VARCHAR(100),
    tags JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- TABLE 2: SYSTEM METRICS
-- =====================================================
CREATE TABLE system_metrics (
    id SERIAL PRIMARY KEY,
    server_ip VARCHAR(15) NOT NULL,
    server_hostname VARCHAR(255),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    cpu_percent DECIMAL(5,2),
    memory_percent DECIMAL(5,2),
    memory_used BIGINT,
    memory_total BIGINT,
    memory_available BIGINT,
    swap_percent DECIMAL(5,2),
    disk_percent DECIMAL(5,2),
    load_avg_1 DECIMAL(5,2),
    load_avg_5 DECIMAL(5,2),
    load_avg_15 DECIMAL(5,2),
    uptime_seconds BIGINT,
    boot_time TIMESTAMP,
    is_remote BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (server_ip) REFERENCES servers(server_ip) ON DELETE CASCADE
);

-- =====================================================
-- TABLE 3: PROCESS METRICS
-- =====================================================
CREATE TABLE process_metrics (
    id SERIAL PRIMARY KEY,
    server_ip VARCHAR(15) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    process_name VARCHAR(255),
    pid INTEGER,
    cpu_percent DECIMAL(5,2),
    memory_percent DECIMAL(5,2),
    status VARCHAR(50),
    command_line TEXT,
    FOREIGN KEY (server_ip) REFERENCES servers(server_ip) ON DELETE CASCADE
);

-- =====================================================
-- TABLE 4: NETWORK METRICS
-- =====================================================
CREATE TABLE network_metrics (
    id SERIAL PRIMARY KEY,
    server_ip VARCHAR(15) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    bytes_sent BIGINT,
    bytes_recv BIGINT,
    packets_sent BIGINT,
    packets_recv BIGINT,
    interface_count INTEGER,
    network_interfaces JSONB,
    FOREIGN KEY (server_ip) REFERENCES servers(server_ip) ON DELETE CASCADE
);

-- =====================================================
-- TABLE 5: DISK METRICS
-- =====================================================
CREATE TABLE disk_metrics (
    id SERIAL PRIMARY KEY,
    server_ip VARCHAR(15) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    disk_usage JSONB, -- Store all disk information as JSON
    total_disk_space BIGINT,
    used_disk_space BIGINT,
    free_disk_space BIGINT,
    disk_percent DECIMAL(5,2),
    FOREIGN KEY (server_ip) REFERENCES servers(server_ip) ON DELETE CASCADE
);

-- =====================================================
-- TABLE 6: SERVER EVENTS
-- =====================================================
CREATE TABLE server_events (
    id SERIAL PRIMARY KEY,
    server_ip VARCHAR(15) NOT NULL,
    event_type VARCHAR(50), -- 'connected', 'disconnected', 'discovered', 'alert'
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    details JSONB,
    severity VARCHAR(20) DEFAULT 'info', -- info, warning, error, critical
    message TEXT,
    FOREIGN KEY (server_ip) REFERENCES servers(server_ip) ON DELETE CASCADE
);

-- =====================================================
-- TABLE 7: ALERTS LOG
-- =====================================================
CREATE TABLE alerts_log (
    id SERIAL PRIMARY KEY,
    server_ip VARCHAR(15) NOT NULL,
    alert_type VARCHAR(100),
    alert_message TEXT,
    severity VARCHAR(20), -- low, medium, high, critical
    threshold_value DECIMAL(10,2),
    current_value DECIMAL(10,2),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP,
    FOREIGN KEY (server_ip) REFERENCES servers(server_ip) ON DELETE CASCADE
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Primary indexes for time-series queries
CREATE INDEX idx_system_metrics_server_time ON system_metrics (server_ip, timestamp DESC);
CREATE INDEX idx_process_metrics_server_time ON process_metrics (server_ip, timestamp DESC);
CREATE INDEX idx_network_metrics_server_time ON network_metrics (server_ip, timestamp DESC);
CREATE INDEX idx_disk_metrics_server_time ON disk_metrics (server_ip, timestamp DESC);
CREATE INDEX idx_server_events_server_time ON server_events (server_ip, timestamp DESC);
CREATE INDEX idx_alerts_log_server_time ON alerts_log (server_ip, timestamp DESC);

-- Additional performance indexes
CREATE INDEX idx_servers_status ON servers (status);
CREATE INDEX idx_servers_last_seen ON servers (last_seen DESC);
CREATE INDEX idx_system_metrics_timestamp ON system_metrics (timestamp DESC);
CREATE INDEX idx_alerts_unresolved ON alerts_log (resolved, severity) WHERE resolved = FALSE;

-- Composite indexes for common queries
CREATE INDEX idx_system_metrics_server_cpu ON system_metrics (server_ip, cpu_percent DESC);
CREATE INDEX idx_system_metrics_server_memory ON system_metrics (server_ip, memory_percent DESC);

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update last_seen timestamp
CREATE OR REPLACE FUNCTION update_server_last_seen()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE servers 
    SET last_seen = CURRENT_TIMESTAMP 
    WHERE server_ip = NEW.server_ip;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers to update last_seen when data is inserted
CREATE TRIGGER trigger_update_last_seen_system
    AFTER INSERT ON system_metrics
    FOR EACH ROW
    EXECUTE FUNCTION update_server_last_seen();

CREATE TRIGGER trigger_update_last_seen_process
    AFTER INSERT ON process_metrics
    FOR EACH ROW
    EXECUTE FUNCTION update_server_last_seen();

CREATE TRIGGER trigger_update_last_seen_network
    AFTER INSERT ON network_metrics
    FOR EACH ROW
    EXECUTE FUNCTION update_server_last_seen();

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- Latest metrics for all servers
CREATE VIEW latest_server_metrics AS
SELECT DISTINCT ON (s.server_ip)
    s.server_ip,
    s.hostname,
    s.server_name,
    s.status,
    s.last_seen,
    sm.timestamp,
    sm.cpu_percent,
    sm.memory_percent,
    sm.disk_percent,
    sm.load_avg_1
FROM servers s
LEFT JOIN system_metrics sm ON s.server_ip = sm.server_ip
ORDER BY s.server_ip, sm.timestamp DESC;

-- Server summary with latest stats
CREATE VIEW server_summary AS
SELECT 
    s.*,
    CASE 
        WHEN s.last_seen > NOW() - INTERVAL '2 minutes' THEN 'online'
        WHEN s.last_seen > NOW() - INTERVAL '10 minutes' THEN 'warning'
        ELSE 'offline'
    END as connection_status,
    lsm.cpu_percent,
    lsm.memory_percent,
    lsm.disk_percent,
    lsm.load_avg_1
FROM servers s
LEFT JOIN latest_server_metrics lsm ON s.server_ip = lsm.server_ip;

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Insert sample server (will be replaced by actual registration)
INSERT INTO servers (server_ip, hostname, server_name, os_info, cpu_cores, total_memory, environment, server_group)
VALUES 
    ('127.0.0.1', 'localhost', 'Local Development Server', 'RHEL 9', 8, 16777216000, 'dev', 'development'),
    ('************', 'aidev01', 'AI Development Server', 'RHEL 9', 8, 16777216000, 'dev', 'ai-servers');

-- =====================================================
-- GRANT FINAL PERMISSIONS
-- =====================================================

-- Ensure resource_user has all necessary permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO resource_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO resource_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO resource_user;

-- Grant permissions on views
GRANT SELECT ON latest_server_metrics TO resource_user;
GRANT SELECT ON server_summary TO resource_user;

-- Success message
SELECT 'Database setup completed successfully!' as status;