#!/usr/bin/env python3.12
"""
Database Models for PinnacleAi Resource Manager
Defines data structures and operations for all database tables
"""

import os
import sys
import json
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_connection import get_database

class ServerStatus(Enum):
    """Server status enumeration"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    MAINTENANCE = "maintenance"
    ERROR = "error"

class AlertSeverity(Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class EventType(Enum):
    """Server event types"""
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    DISCOVERED = "discovered"
    ALERT = "alert"
    ERROR = "error"

@dataclass
class Server:
    """Server model"""
    server_ip: str
    hostname: str = None
    server_name: str = None
    os_info: str = None
    cpu_cores: int = None
    total_memory: int = None
    status: str = ServerStatus.ACTIVE.value
    location: str = None
    environment: str = None
    server_group: str = None
    tags: Dict = None
    id: int = None
    first_seen: datetime = None
    last_seen: datetime = None
    created_at: datetime = None
    updated_at: datetime = None

@dataclass
class SystemMetrics:
    """System metrics model"""
    server_ip: str
    server_hostname: str = None
    cpu_percent: float = None
    memory_percent: float = None
    memory_used: int = None
    memory_total: int = None
    memory_available: int = None
    swap_percent: float = None
    disk_percent: float = None
    load_avg_1: float = None
    load_avg_5: float = None
    load_avg_15: float = None
    uptime_seconds: int = None
    boot_time: datetime = None
    is_remote: bool = False
    timestamp: datetime = None
    id: int = None

@dataclass
class ProcessMetrics:
    """Process metrics model"""
    server_ip: str
    process_name: str
    pid: int
    cpu_percent: float = None
    memory_percent: float = None
    status: str = None
    command_line: str = None
    timestamp: datetime = None
    id: int = None

@dataclass
class NetworkMetrics:
    """Network metrics model"""
    server_ip: str
    bytes_sent: int = None
    bytes_recv: int = None
    packets_sent: int = None
    packets_recv: int = None
    interface_count: int = None
    network_interfaces: Dict = None
    timestamp: datetime = None
    id: int = None

@dataclass
class DiskMetrics:
    """Disk metrics model"""
    server_ip: str
    disk_usage: Dict = None
    total_disk_space: int = None
    used_disk_space: int = None
    free_disk_space: int = None
    disk_percent: float = None
    timestamp: datetime = None
    id: int = None

@dataclass
class ServerEvent:
    """Server event model"""
    server_ip: str
    event_type: str
    details: Dict = None
    severity: str = "info"
    message: str = None
    timestamp: datetime = None
    id: int = None

@dataclass
class Alert:
    """Alert model"""
    server_ip: str
    alert_type: str
    alert_message: str
    severity: str
    threshold_value: float = None
    current_value: float = None
    resolved: bool = False
    resolved_at: datetime = None
    timestamp: datetime = None
    id: int = None

class DatabaseOperations:
    """Database operations for all models"""
    
    def __init__(self):
        self.db = get_database()
    
    # ==================== SERVER OPERATIONS ====================
    
    def register_server(self, server: Server) -> bool:
        """Register or update a server"""
        query = """
        INSERT INTO servers (server_ip, hostname, server_name, os_info, cpu_cores, 
                           total_memory, status, location, environment, server_group, tags)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (server_ip) 
        DO UPDATE SET 
            hostname = EXCLUDED.hostname,
            server_name = EXCLUDED.server_name,
            os_info = EXCLUDED.os_info,
            cpu_cores = EXCLUDED.cpu_cores,
            total_memory = EXCLUDED.total_memory,
            status = EXCLUDED.status,
            location = EXCLUDED.location,
            environment = EXCLUDED.environment,
            server_group = EXCLUDED.server_group,
            tags = EXCLUDED.tags,
            last_seen = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        """
        
        params = (
            server.server_ip, server.hostname, server.server_name,
            server.os_info, server.cpu_cores, server.total_memory,
            server.status, server.location, server.environment,
            server.server_group, json.dumps(server.tags) if server.tags else None
        )
        
        return self.db.execute_insert(query, params)
    
    def get_server(self, server_ip: str) -> Optional[Server]:
        """Get server by IP"""
        query = "SELECT * FROM servers WHERE server_ip = %s"
        result = self.db.execute_query(query, (server_ip,))
        
        if result:
            row = result[0]
            return Server(
                id=row[0], server_ip=row[1], hostname=row[2], server_name=row[3],
                os_info=row[4], cpu_cores=row[5], total_memory=row[6], status=row[7],
                first_seen=row[8], last_seen=row[9], location=row[10],
                environment=row[11], server_group=row[12],
                tags=json.loads(row[13]) if row[13] else None,
                created_at=row[14], updated_at=row[15]
            )
        return None
    
    def get_all_servers(self) -> List[Server]:
        """Get all registered servers"""
        query = "SELECT * FROM servers ORDER BY server_ip"
        results = self.db.execute_query(query)
        
        servers = []
        if results:
            for row in results:
                servers.append(Server(
                    id=row[0], server_ip=row[1], hostname=row[2], server_name=row[3],
                    os_info=row[4], cpu_cores=row[5], total_memory=row[6], status=row[7],
                    first_seen=row[8], last_seen=row[9], location=row[10],
                    environment=row[11], server_group=row[12],
                    tags=json.loads(row[13]) if row[13] else None,
                    created_at=row[14], updated_at=row[15]
                ))
        return servers
    
    def update_server_status(self, server_ip: str, status: str) -> bool:
        """Update server status"""
        query = """
        UPDATE servers 
        SET status = %s, last_seen = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
        WHERE server_ip = %s
        """
        return self.db.execute_insert(query, (status, server_ip))
    
    # ==================== SYSTEM METRICS OPERATIONS ====================
    
    def store_system_metrics(self, metrics: SystemMetrics) -> bool:
        """Store system metrics"""
        query = """
        INSERT INTO system_metrics (
            server_ip, server_hostname, cpu_percent, memory_percent, memory_used,
            memory_total, memory_available, swap_percent, disk_percent,
            load_avg_1, load_avg_5, load_avg_15, uptime_seconds, boot_time, is_remote
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            metrics.server_ip, metrics.server_hostname, metrics.cpu_percent,
            metrics.memory_percent, metrics.memory_used, metrics.memory_total,
            metrics.memory_available, metrics.swap_percent, metrics.disk_percent,
            metrics.load_avg_1, metrics.load_avg_5, metrics.load_avg_15,
            metrics.uptime_seconds, metrics.boot_time, metrics.is_remote
        )
        
        return self.db.execute_insert(query, params)
    
    def get_latest_system_metrics(self, server_ip: str) -> Optional[SystemMetrics]:
        """Get latest system metrics for a server"""
        query = """
        SELECT * FROM system_metrics 
        WHERE server_ip = %s 
        ORDER BY timestamp DESC 
        LIMIT 1
        """
        result = self.db.execute_query(query, (server_ip,))
        
        if result:
            row = result[0]
            return SystemMetrics(
                id=row[0], server_ip=row[1], server_hostname=row[2],
                timestamp=row[3], cpu_percent=row[4], memory_percent=row[5],
                memory_used=row[6], memory_total=row[7], memory_available=row[8],
                swap_percent=row[9], disk_percent=row[10], load_avg_1=row[11],
                load_avg_5=row[12], load_avg_15=row[13], uptime_seconds=row[14],
                boot_time=row[15], is_remote=row[16]
            )
        return None
    
    def get_system_metrics_history(self, server_ip: str, hours: int = 24) -> List[SystemMetrics]:
        """Get system metrics history for a server"""
        query = """
        SELECT * FROM system_metrics 
        WHERE server_ip = %s 
        AND timestamp > NOW() - INTERVAL '%s hours'
        ORDER BY timestamp DESC
        """
        results = self.db.execute_query(query, (server_ip, hours))
        
        metrics_list = []
        if results:
            for row in results:
                metrics_list.append(SystemMetrics(
                    id=row[0], server_ip=row[1], server_hostname=row[2],
                    timestamp=row[3], cpu_percent=row[4], memory_percent=row[5],
                    memory_used=row[6], memory_total=row[7], memory_available=row[8],
                    swap_percent=row[9], disk_percent=row[10], load_avg_1=row[11],
                    load_avg_5=row[12], load_avg_15=row[13], uptime_seconds=row[14],
                    boot_time=row[15], is_remote=row[16]
                ))
        return metrics_list
    
    # ==================== PROCESS METRICS OPERATIONS ====================
    
    def store_process_metrics(self, processes: List[ProcessMetrics]) -> bool:
        """Store process metrics (batch insert)"""
        if not processes:
            return True
            
        query = """
        INSERT INTO process_metrics (
            server_ip, process_name, pid, cpu_percent, memory_percent, status, command_line
        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        
        params_list = []
        for process in processes:
            params_list.append((
                process.server_ip, process.process_name, process.pid,
                process.cpu_percent, process.memory_percent, process.status,
                process.command_line
            ))
        
        return self.db.execute_batch_insert(query, params_list)
    
    # ==================== NETWORK METRICS OPERATIONS ====================
    
    def store_network_metrics(self, metrics: NetworkMetrics) -> bool:
        """Store network metrics"""
        query = """
        INSERT INTO network_metrics (
            server_ip, bytes_sent, bytes_recv, packets_sent, packets_recv,
            interface_count, network_interfaces
        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            metrics.server_ip, metrics.bytes_sent, metrics.bytes_recv,
            metrics.packets_sent, metrics.packets_recv, metrics.interface_count,
            json.dumps(metrics.network_interfaces) if metrics.network_interfaces else None
        )
        
        return self.db.execute_insert(query, params)
    
    # ==================== DISK METRICS OPERATIONS ====================
    
    def store_disk_metrics(self, metrics: DiskMetrics) -> bool:
        """Store disk metrics"""
        query = """
        INSERT INTO disk_metrics (
            server_ip, disk_usage, total_disk_space, used_disk_space,
            free_disk_space, disk_percent
        ) VALUES (%s, %s, %s, %s, %s, %s)
        """
        
        params = (
            metrics.server_ip, json.dumps(metrics.disk_usage) if metrics.disk_usage else None,
            metrics.total_disk_space, metrics.used_disk_space,
            metrics.free_disk_space, metrics.disk_percent
        )
        
        return self.db.execute_insert(query, params)
    
    # ==================== EVENT OPERATIONS ====================
    
    def log_server_event(self, event: ServerEvent) -> bool:
        """Log a server event"""
        query = """
        INSERT INTO server_events (server_ip, event_type, details, severity, message)
        VALUES (%s, %s, %s, %s, %s)
        """
        
        params = (
            event.server_ip, event.event_type,
            json.dumps(event.details) if event.details else None,
            event.severity, event.message
        )
        
        return self.db.execute_insert(query, params)
    
    # ==================== ALERT OPERATIONS ====================
    
    def create_alert(self, alert: Alert) -> bool:
        """Create a new alert"""
        query = """
        INSERT INTO alerts_log (
            server_ip, alert_type, alert_message, severity,
            threshold_value, current_value
        ) VALUES (%s, %s, %s, %s, %s, %s)
        """
        
        params = (
            alert.server_ip, alert.alert_type, alert.alert_message,
            alert.severity, alert.threshold_value, alert.current_value
        )
        
        return self.db.execute_insert(query, params)
    
    def get_active_alerts(self, server_ip: str = None) -> List[Alert]:
        """Get active (unresolved) alerts"""
        if server_ip:
            query = """
            SELECT * FROM alerts_log 
            WHERE server_ip = %s AND resolved = FALSE 
            ORDER BY timestamp DESC
            """
            results = self.db.execute_query(query, (server_ip,))
        else:
            query = """
            SELECT * FROM alerts_log 
            WHERE resolved = FALSE 
            ORDER BY timestamp DESC
            """
            results = self.db.execute_query(query)
        
        alerts = []
        if results:
            for row in results:
                alerts.append(Alert(
                    id=row[0], server_ip=row[1], alert_type=row[2],
                    alert_message=row[3], severity=row[4], threshold_value=row[5],
                    current_value=row[6], timestamp=row[7], resolved=row[8],
                    resolved_at=row[9]
                ))
        return alerts
    
    # ==================== UTILITY OPERATIONS ====================
    
    def cleanup_old_data(self, days: int = 30) -> bool:
        """Clean up old data based on retention policy"""
        tables = ['system_metrics', 'process_metrics', 'network_metrics', 'disk_metrics']
        
        try:
            for table in tables:
                query = f"""
                DELETE FROM {table} 
                WHERE timestamp < NOW() - INTERVAL '%s days'
                """
                self.db.execute_insert(query, (days,))
            
            # Clean up old resolved alerts
            query = """
            DELETE FROM alerts_log 
            WHERE resolved = TRUE AND resolved_at < NOW() - INTERVAL '%s days'
            """
            self.db.execute_insert(query, (days,))
            
            return True
        except Exception as e:
            print(f"Error cleaning up old data: {e}")
            return False
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        stats = {}

        tables = {
            'servers': 'servers',
            'system_metrics': 'system_metrics',
            'process_metrics': 'process_metrics',
            'network_metrics': 'network_metrics',
            'disk_metrics': 'disk_metrics',
            'server_events': 'server_events',
            'alerts_log': 'alerts_log'
        }

        for name, table in tables.items():
            query = f"SELECT COUNT(*) FROM {table}"
            result = self.db.execute_query(query)
            stats[name] = result[0][0] if result else 0

        return stats

    def get_recent_table_data(self, table_name: str, limit: int = 10) -> Dict[str, Any]:
        """Get recent data from any table for dashboard viewing"""
        try:
            # Define table configurations
            table_configs = {
                'servers': {
                    'query': """
                        SELECT server_ip, hostname, server_name, status, last_seen,
                               cpu_cores, total_memory, environment
                        FROM servers
                        ORDER BY last_seen DESC NULLS LAST, server_ip
                        LIMIT %s
                    """,
                    'columns': ['Server IP', 'Hostname', 'Server Name', 'Status', 'Last Seen',
                               'CPU Cores', 'Total Memory', 'Environment']
                },
                'system_metrics': {
                    'query': """
                        SELECT server_ip, server_hostname, timestamp, cpu_percent,
                               memory_percent, disk_percent, load_avg_1, is_remote
                        FROM system_metrics
                        ORDER BY timestamp DESC
                        LIMIT %s
                    """,
                    'columns': ['Server IP', 'Hostname', 'Timestamp', 'CPU %',
                               'Memory %', 'Disk %', 'Load Avg', 'Remote']
                },
                'process_metrics': {
                    'query': """
                        SELECT server_ip, process_name, pid, cpu_percent,
                               memory_percent, status, timestamp
                        FROM process_metrics
                        ORDER BY timestamp DESC
                        LIMIT %s
                    """,
                    'columns': ['Server IP', 'Process', 'PID', 'CPU %',
                               'Memory %', 'Status', 'Timestamp']
                },
                'network_metrics': {
                    'query': """
                        SELECT server_ip, bytes_sent, bytes_recv, packets_sent,
                               packets_recv, interface_count, timestamp
                        FROM network_metrics
                        ORDER BY timestamp DESC
                        LIMIT %s
                    """,
                    'columns': ['Server IP', 'Bytes Sent', 'Bytes Recv', 'Packets Sent',
                               'Packets Recv', 'Interfaces', 'Timestamp']
                },
                'disk_metrics': {
                    'query': """
                        SELECT server_ip, total_disk_space, used_disk_space,
                               free_disk_space, disk_percent, timestamp
                        FROM disk_metrics
                        ORDER BY timestamp DESC
                        LIMIT %s
                    """,
                    'columns': ['Server IP', 'Total Space', 'Used Space',
                               'Free Space', 'Disk %', 'Timestamp']
                },
                'server_events': {
                    'query': """
                        SELECT server_ip, event_type, message, severity, timestamp
                        FROM server_events
                        ORDER BY timestamp DESC
                        LIMIT %s
                    """,
                    'columns': ['Server IP', 'Event Type', 'Message', 'Severity', 'Timestamp']
                },
                'alerts_log': {
                    'query': """
                        SELECT server_ip, alert_type, alert_message, severity,
                               current_value, threshold_value, resolved, timestamp
                        FROM alerts_log
                        ORDER BY timestamp DESC
                        LIMIT %s
                    """,
                    'columns': ['Server IP', 'Alert Type', 'Message', 'Severity',
                               'Current Value', 'Threshold', 'Resolved', 'Timestamp']
                }
            }

            if table_name not in table_configs:
                return {
                    'success': False,
                    'error': f'Table {table_name} not supported',
                    'data': [],
                    'columns': []
                }

            config = table_configs[table_name]
            results = self.db.execute_query(config['query'], (limit,))

            # Format the data
            formatted_data = []
            if results:
                for row in results:
                    formatted_row = []
                    for value in row:
                        if isinstance(value, datetime):
                            formatted_row.append(value.strftime('%Y-%m-%d %H:%M:%S'))
                        elif isinstance(value, (int, float)) and value is not None:
                            # Format large numbers
                            if isinstance(value, int) and value > 1000000:
                                formatted_row.append(f"{value:,}")
                            elif isinstance(value, float):
                                formatted_row.append(f"{value:.2f}")
                            else:
                                formatted_row.append(str(value))
                        else:
                            formatted_row.append(str(value) if value is not None else 'N/A')
                    formatted_data.append(formatted_row)

            return {
                'success': True,
                'table_name': table_name,
                'columns': config['columns'],
                'data': formatted_data,
                'row_count': len(formatted_data)
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'Error fetching table data: {str(e)}',
                'data': [],
                'columns': []
            }

# Global database operations instance
_db_ops_instance = None

def get_db_operations() -> DatabaseOperations:
    """Get global database operations instance"""
    global _db_ops_instance
    if _db_ops_instance is None:
        _db_ops_instance = DatabaseOperations()
    return _db_ops_instance

if __name__ == "__main__":
    # Test database models
    print("🔍 Testing database models...")
    
    db_ops = DatabaseOperations()
    
    # Test server registration
    test_server = Server(
        server_ip="127.0.0.1",
        hostname="localhost",
        server_name="Test Server",
        os_info="RHEL 9",
        cpu_cores=8,
        total_memory=16777216000,
        environment="test"
    )
    
    if db_ops.register_server(test_server):
        print("✅ Server registration test successful")
    else:
        print("❌ Server registration test failed")
    
    # Test getting server
    server = db_ops.get_server("127.0.0.1")
    if server:
        print(f"✅ Server retrieval test successful: {server.hostname}")
    else:
        print("❌ Server retrieval test failed")
    
    print("🎯 Database models test completed!")