# 🗄️ **<PERSON><PERSON><PERSON><PERSON><PERSON> DOCUMENTATION - COMPLETE SCHEMA & DATA**

## **📊 PostgreSQL Database: `resource_monitor`**

---

# **🏗️ DATABASE ARCHITECTURE**

## **📋 Database Overview:**
- **Database Name:** `resource_monitor`
- **Database Engine:** PostgreSQL 13+
- **Total Tables:** 7 tables
- **Connection Pool:** 20 connections
- **Backup Enabled:** Yes (daily at 2 AM)

---

# **📊 COMPLETE DATABASE SCHEMA**

## **🖥️ Table 1: `servers`**
**Purpose:** Server registry and metadata

```sql
CREATE TABLE servers (
    id SERIAL PRIMARY KEY,
    server_ip VARCHAR(45) UNIQUE NOT NULL,
    hostname VA<PERSON>HAR(255),
    server_name VARCHAR(255),
    os_info TEXT,
    cpu_cores INTEGER,
    total_memory BIGINT,
    status VARCHAR(20) DEFAULT 'active',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    location VARCHAR(255),
    environment VARCHAR(50),
    server_group VARCHAR(100),
    tags JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_servers_ip ON servers(server_ip);
CREATE INDEX idx_servers_status ON servers(status);
CREATE INDEX idx_servers_environment ON servers(environment);
```

### **📊 Current Data in `servers`:**