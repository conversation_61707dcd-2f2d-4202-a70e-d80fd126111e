#!/usr/bin/env python3.12
"""
Phase 2 Test Script - Core Database Integration
Tests the enhanced web_dashboard.py with database integration
"""

import os
import sys
import time
import threading
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dashboard_data_initialization():
    """Test DashboardData initialization with database"""
    print("🔍 Testing DashboardData Initialization...")
    try:
        from web_dashboard import DashboardData
        
        dashboard = DashboardData()
        
        # Check if database integration is enabled
        if hasattr(dashboard, 'database_enabled') and dashboard.database_enabled:
            print("✅ Database integration: ENABLED")
            print(f"   Server IP: {dashboard.server_ip}")
            print(f"   Hostname: {dashboard.hostname}")
            print(f"   Database operations: {'Available' if dashboard.db_ops else 'Not Available'}")
            print(f"   Thread executor: {'Available' if dashboard.db_executor else 'Not Available'}")
            return True
        else:
            print("⚠️  Database integration: DISABLED")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard initialization error: {e}")
        return False

def test_server_registration():
    """Test server registration in database"""
    print("\n🔍 Testing Server Registration...")
    try:
        from web_dashboard import DashboardData
        from database.db_models import DatabaseOperations
        
        dashboard = DashboardData()
        
        if not dashboard.database_enabled:
            print("⚠️  Database not enabled, skipping test")
            return False
        
        # Check if server was registered
        db_ops = DatabaseOperations()
        server = db_ops.get_server(dashboard.server_ip)
        
        if server:
            print(f"✅ Server registration: SUCCESS")
            print(f"   IP: {server.server_ip}")
            print(f"   Hostname: {server.hostname}")
            print(f"   OS: {server.os_info}")
            print(f"   CPU Cores: {server.cpu_cores}")
            print(f"   Memory: {server.total_memory}")
            return True
        else:
            print("❌ Server registration: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Server registration error: {e}")
        return False

def test_data_collection_and_storage():
    """Test data collection and database storage"""
    print("\n🔍 Testing Data Collection and Storage...")
    try:
        from web_dashboard import DashboardData
        from database.db_models import DatabaseOperations
        
        dashboard = DashboardData()
        
        if not dashboard.database_enabled:
            print("⚠️  Database not enabled, skipping test")
            return False
        
        # Get initial record count
        db_ops = DatabaseOperations()
        initial_stats = db_ops.get_database_stats()
        initial_system_metrics = initial_stats.get('system_metrics', 0)
        
        print(f"📊 Initial system metrics records: {initial_system_metrics}")
        
        # Trigger data update (simulates the 5-second update)
        print("🔄 Triggering data update...")
        dashboard.update_data()
        
        # Wait a moment for background database operations
        time.sleep(2)
        
        # Check if new data was stored
        final_stats = db_ops.get_database_stats()
        final_system_metrics = final_stats.get('system_metrics', 0)
        
        print(f"📊 Final system metrics records: {final_system_metrics}")
        
        if final_system_metrics > initial_system_metrics:
            print("✅ Data storage: SUCCESS")
            print(f"   New records added: {final_system_metrics - initial_system_metrics}")
            
            # Get the latest metrics to verify data
            latest_metrics = db_ops.get_latest_system_metrics(dashboard.server_ip)
            if latest_metrics:
                print(f"   Latest CPU: {latest_metrics.cpu_percent}%")
                print(f"   Latest Memory: {latest_metrics.memory_percent}%")
                print(f"   Latest Disk: {latest_metrics.disk_percent}%")
            
            return True
        else:
            print("❌ Data storage: NO NEW RECORDS")
            return False
            
    except Exception as e:
        print(f"❌ Data collection error: {e}")
        return False

def test_continuous_monitoring():
    """Test continuous monitoring for a short period"""
    print("\n🔍 Testing Continuous Monitoring (10 seconds)...")
    try:
        from web_dashboard import DashboardData
        from database.db_models import DatabaseOperations
        
        dashboard = DashboardData()
        
        if not dashboard.database_enabled:
            print("⚠️  Database not enabled, skipping test")
            return False
        
        db_ops = DatabaseOperations()
        initial_stats = db_ops.get_database_stats()
        initial_count = initial_stats.get('system_metrics', 0)
        
        print(f"📊 Starting continuous monitoring...")
        print(f"   Initial records: {initial_count}")
        
        # Start monitoring
        dashboard.start_monitoring()
        
        # Monitor for 10 seconds
        for i in range(10):
            time.sleep(1)
            print(f"   Monitoring... {i+1}/10 seconds")
        
        # Stop monitoring
        dashboard.stop_monitoring()
        
        # Wait for final database operations
        time.sleep(2)
        
        # Check final count
        final_stats = db_ops.get_database_stats()
        final_count = final_stats.get('system_metrics', 0)
        
        records_added = final_count - initial_count
        print(f"📊 Monitoring completed:")
        print(f"   Final records: {final_count}")
        print(f"   Records added: {records_added}")
        
        # We expect at least 1-2 records (monitoring runs every 5 seconds)
        if records_added >= 1:
            print("✅ Continuous monitoring: SUCCESS")
            return True
        else:
            print("❌ Continuous monitoring: NO NEW RECORDS")
            return False
            
    except Exception as e:
        print(f"❌ Continuous monitoring error: {e}")
        return False

def test_database_performance():
    """Test database performance with multiple updates"""
    print("\n🔍 Testing Database Performance...")
    try:
        from web_dashboard import DashboardData
        from database.db_models import DatabaseOperations
        
        dashboard = DashboardData()
        
        if not dashboard.database_enabled:
            print("⚠️  Database not enabled, skipping test")
            return False
        
        db_ops = DatabaseOperations()
        
        # Measure time for multiple updates
        start_time = time.time()
        
        print("🚀 Performing 5 rapid data updates...")
        for i in range(5):
            dashboard.update_data()
            print(f"   Update {i+1}/5 completed")
            time.sleep(0.5)  # Small delay between updates
        
        # Wait for all background operations to complete
        time.sleep(3)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"⏱️  Performance Results:")
        print(f"   Total time: {total_time:.2f} seconds")
        print(f"   Average per update: {total_time/5:.2f} seconds")
        
        # Check if data was stored
        latest_metrics = db_ops.get_latest_system_metrics(dashboard.server_ip)
        if latest_metrics:
            print(f"   Latest data timestamp: {latest_metrics.timestamp}")
            print("✅ Database performance: GOOD")
            return True
        else:
            print("❌ Database performance: NO DATA STORED")
            return False
            
    except Exception as e:
        print(f"❌ Database performance error: {e}")
        return False

def main():
    """Run all Phase 2 tests"""
    print("🚀 PHASE 2 CORE DATABASE INTEGRATION - COMPREHENSIVE TEST")
    print("=" * 65)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 65)
    
    tests = [
        ("Dashboard Data Initialization", test_dashboard_data_initialization),
        ("Server Registration", test_server_registration),
        ("Data Collection and Storage", test_data_collection_and_storage),
        ("Continuous Monitoring", test_continuous_monitoring),
        ("Database Performance", test_database_performance)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: EXCEPTION - {e}")
            failed += 1
        
        time.sleep(1)  # Small delay between tests
    
    print("\n" + "=" * 65)
    print("📊 PHASE 2 TEST RESULTS")
    print("=" * 65)
    print(f"✅ Tests Passed: {passed}")
    print(f"❌ Tests Failed: {failed}")
    print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! Phase 2 integration successful!")
        print("🚀 Your dashboard now stores data in PostgreSQL every 5 seconds!")
        print("📊 Historical data is being preserved for analysis!")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review and fix issues.")
    
    print("=" * 65)

if __name__ == "__main__":
    main()