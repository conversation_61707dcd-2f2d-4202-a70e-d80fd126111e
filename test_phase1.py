#!/usr/bin/env python3.12
"""
Phase 1 Test Script - Database Foundation
Tests all Phase 1 components to ensure they're working correctly
"""

import os
import sys
import time
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """Test database connection"""
    print("🔍 Testing Database Connection...")
    try:
        from database.db_connection import test_database_connection
        if test_database_connection():
            print("✅ Database connection: PASSED")
            return True
        else:
            print("❌ Database connection: FAILED")
            return False
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def test_database_models():
    """Test database models and operations"""
    print("\n🔍 Testing Database Models...")
    try:
        from database.db_models import DatabaseOperations, Server, SystemMetrics
        
        db_ops = DatabaseOperations()
        
        # Test server registration
        test_server = Server(
            server_ip="*************",
            hostname="test-server",
            server_name="Phase 1 Test Server",
            os_info="RHEL 9.2",
            cpu_cores=4,
            total_memory=8589934592,
            environment="test",
            server_group="testing"
        )
        
        if db_ops.register_server(test_server):
            print("✅ Server registration: PASSED")
        else:
            print("❌ Server registration: FAILED")
            return False
        
        # Test server retrieval
        retrieved_server = db_ops.get_server("*************")
        if retrieved_server and retrieved_server.hostname == "test-server":
            print("✅ Server retrieval: PASSED")
        else:
            print("❌ Server retrieval: FAILED")
            return False
        
        # Test system metrics storage
        test_metrics = SystemMetrics(
            server_ip="*************",
            server_hostname="test-server",
            cpu_percent=25.5,
            memory_percent=45.2,
            memory_used=3865470976,
            memory_total=8589934592,
            memory_available=4724463616,
            disk_percent=35.8,
            load_avg_1=1.2,
            load_avg_5=1.1,
            load_avg_15=0.9
        )
        
        if db_ops.store_system_metrics(test_metrics):
            print("✅ System metrics storage: PASSED")
        else:
            print("❌ System metrics storage: FAILED")
            return False
        
        # Test metrics retrieval
        latest_metrics = db_ops.get_latest_system_metrics("*************")
        if latest_metrics and latest_metrics.cpu_percent == 25.5:
            print("✅ System metrics retrieval: PASSED")
        else:
            print("❌ System metrics retrieval: FAILED")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Database models error: {e}")
        return False

def test_database_config():
    """Test database configuration"""
    print("\n🔍 Testing Database Configuration...")
    try:
        from config.database_config import get_database_config, DatabaseConfig, AlertThresholds
        
        config = get_database_config()
        
        # Check if all required config sections exist
        required_sections = ['connection', 'retention', 'performance', 'thresholds', 'tables', 'views']
        for section in required_sections:
            if section not in config:
                print(f"❌ Missing config section: {section}")
                return False
        
        # Check connection config
        conn_config = config['connection']
        required_conn_keys = ['host', 'port', 'database', 'user', 'password']
        for key in required_conn_keys:
            if key not in conn_config:
                print(f"❌ Missing connection config: {key}")
                return False
        
        # Check alert thresholds
        thresholds = config['thresholds']
        required_metrics = ['cpu', 'memory', 'disk', 'load']
        for metric in required_metrics:
            if metric not in thresholds:
                print(f"❌ Missing threshold for: {metric}")
                return False
            if 'warning' not in thresholds[metric] or 'critical' not in thresholds[metric]:
                print(f"❌ Missing warning/critical thresholds for: {metric}")
                return False
        
        print("✅ Database configuration: PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Database configuration error: {e}")
        return False

def test_database_stats():
    """Test database statistics"""
    print("\n🔍 Testing Database Statistics...")
    try:
        from database.db_models import DatabaseOperations
        
        db_ops = DatabaseOperations()
        stats = db_ops.get_database_stats()
        
        print("📊 Database Statistics:")
        for table, count in stats.items():
            print(f"   {table}: {count} records")
        
        # Check if we have some data
        if stats.get('servers', 0) > 0:
            print("✅ Database statistics: PASSED")
            return True
        else:
            print("❌ Database statistics: No servers found")
            return False
            
    except Exception as e:
        print(f"❌ Database statistics error: {e}")
        return False

def test_database_views():
    """Test database views"""
    print("\n🔍 Testing Database Views...")
    try:
        from database.db_connection import get_database
        
        db = get_database()
        
        # Test server_summary view
        result = db.execute_query("SELECT COUNT(*) FROM server_summary")
        if result and result[0][0] > 0:
            print("✅ server_summary view: PASSED")
        else:
            print("❌ server_summary view: No data")
            return False
        
        # Test latest_server_metrics view
        result = db.execute_query("SELECT COUNT(*) FROM latest_server_metrics")
        if result is not None:  # View exists even if no data
            print("✅ latest_server_metrics view: PASSED")
        else:
            print("❌ latest_server_metrics view: FAILED")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Database views error: {e}")
        return False

def main():
    """Run all Phase 1 tests"""
    print("🚀 PHASE 1 DATABASE FOUNDATION - COMPREHENSIVE TEST")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Database Models", test_database_models),
        ("Database Configuration", test_database_config),
        ("Database Statistics", test_database_stats),
        ("Database Views", test_database_views)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: EXCEPTION - {e}")
            failed += 1
        
        time.sleep(0.5)  # Small delay between tests
    
    print("\n" + "=" * 60)
    print("📊 PHASE 1 TEST RESULTS")
    print("=" * 60)
    print(f"✅ Tests Passed: {passed}")
    print(f"❌ Tests Failed: {failed}")
    print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! Phase 1 is ready for Phase 2!")
        print("🚀 Database foundation is solid and ready for integration!")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review and fix issues before proceeding.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()