#!/usr/local/bin/python3.12
"""
Phase 3 Test Script - Multi-Server Data Collection
Tests the multi-server data collection functionality
"""

import os
import sys
import time
import threading
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_multi_server_collector_initialization():
    """Test multi-server collector initialization"""
    print("🔍 Testing Multi-Server Collector Initialization...")
    try:
        from core.multi_server_collector import MultiServerCollector
        
        collector = MultiServerCollector()
        
        print(f"✅ Multi-server collector created")
        print(f"   Database available: {'Yes' if collector.db_ops else 'No'}")
        print(f"   Server discovery available: {'Yes' if collector.server_discovery else 'No'}")
        print(f"   Thread executor available: {'Yes' if collector.executor else 'No'}")
        
        # Test status
        status = collector.get_collection_status()
        print(f"   Collection status: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Multi-server collector initialization error: {e}")
        return False

def test_dashboard_integration():
    """Test dashboard integration with multi-server collector"""
    print("\n🔍 Testing Dashboard Integration...")
    try:
        from web_dashboard import DashboardData
        
        dashboard = DashboardData()
        
        print(f"✅ Dashboard initialized")
        print(f"   Database enabled: {dashboard.database_enabled}")
        print(f"   Multi-server enabled: {dashboard.multi_server_enabled}")
        
        if dashboard.multi_server_enabled:
            print(f"   Multi-server collector: {'Available' if dashboard.multi_server_collector else 'Not Available'}")
            
            # Test collection status
            if dashboard.multi_server_collector:
                status = dashboard.multi_server_collector.get_collection_status()
                print(f"   Collection status: {status['status']}")
                print(f"   Connected servers: {status['servers']}")
                print(f"   Collection interval: {status['interval']}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Dashboard integration error: {e}")
        return False

def test_server_discovery_integration():
    """Test server discovery integration"""
    print("\n🔍 Testing Server Discovery Integration...")
    try:
        from core.server_discovery import ServerDiscovery
        from database.db_models import DatabaseOperations
        
        discovery = ServerDiscovery()
        db_ops = DatabaseOperations()
        
        # Get connected servers
        connected_servers = discovery.get_connected_servers_info()
        print(f"📊 Connected servers found: {len(connected_servers)}")
        
        for ip, info in connected_servers.items():
            print(f"   {ip}: {info.get('name', 'Unknown')} - SSH: {info.get('ssh_connected', False)}")
        
        # Check database registration
        all_servers = db_ops.get_all_servers()
        print(f"📊 Servers in database: {len(all_servers)}")
        
        for server in all_servers:
            print(f"   {server.server_ip}: {server.hostname} - Status: {server.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Server discovery integration error: {e}")
        return False

def test_multi_server_data_collection():
    """Test actual multi-server data collection"""
    print("\n🔍 Testing Multi-Server Data Collection...")
    try:
        from core.multi_server_collector import MultiServerCollector
        from database.db_models import DatabaseOperations
        
        collector = MultiServerCollector()
        db_ops = DatabaseOperations()
        
        if not collector.db_ops or not collector.server_discovery:
            print("⚠️  Required components not available, skipping test")
            return False
        
        # Get initial database stats
        initial_stats = db_ops.get_database_stats()
        initial_system = initial_stats.get('system_metrics', 0)
        
        print(f"📊 Initial system metrics: {initial_system}")
        
        # Start collection for a short period
        print("🔄 Starting multi-server collection for 35 seconds...")
        collector.start_collection()
        
        # Wait for collection cycles
        for i in range(35):
            time.sleep(1)
            if i % 10 == 0:
                print(f"   Collection running... {i+1}/35 seconds")
        
        # Stop collection
        collector.stop_collection()
        
        # Wait for final operations
        time.sleep(3)
        
        # Check final stats
        final_stats = db_ops.get_database_stats()
        final_system = final_stats.get('system_metrics', 0)
        
        print(f"📊 Final system metrics: {final_system}")
        print(f"📈 New records added: {final_system - initial_system}")
        
        # Check for remote server data
        remote_data_count = 0
        all_servers = db_ops.get_all_servers()
        
        for server in all_servers:
            latest_metrics = db_ops.get_latest_system_metrics(server.server_ip)
            if latest_metrics and latest_metrics.is_remote:
                remote_data_count += 1
                print(f"   Remote data from {server.server_ip}: CPU={latest_metrics.cpu_percent}%")
        
        print(f"📊 Servers with remote data: {remote_data_count}")
        
        if final_system > initial_system:
            print("✅ Multi-server data collection: SUCCESS")
            return True
        else:
            print("⚠️  Multi-server data collection: NO NEW DATA")
            return False
        
    except Exception as e:
        print(f"❌ Multi-server data collection error: {e}")
        return False

def test_api_endpoints():
    """Test new API endpoints"""
    print("\n🔍 Testing New API Endpoints...")
    try:
        from web_dashboard import DashboardData
        
        dashboard = DashboardData()
        
        if not dashboard.database_enabled:
            print("⚠️  Database not enabled, skipping API tests")
            return False
        
        # Test multi-server status
        if dashboard.multi_server_enabled and dashboard.multi_server_collector:
            status = dashboard.multi_server_collector.get_collection_status()
            print(f"✅ Multi-server status API data:")
            print(f"   Status: {status['status']}")
            print(f"   Servers: {status['servers']}")
            print(f"   Interval: {status['interval']}s")
        
        # Test all servers data
        db_ops = dashboard.db_ops
        servers = db_ops.get_all_servers()
        
        print(f"✅ All servers data API:")
        print(f"   Total servers: {len(servers)}")
        
        servers_with_data = 0
        for server in servers:
            latest_metrics = db_ops.get_latest_system_metrics(server.server_ip)
            if latest_metrics:
                servers_with_data += 1
                print(f"   {server.server_ip}: CPU={latest_metrics.cpu_percent}%, Remote={latest_metrics.is_remote}")
        
        print(f"   Servers with data: {servers_with_data}/{len(servers)}")
        
        return True
        
    except Exception as e:
        print(f"❌ API endpoints test error: {e}")
        return False

def test_data_segregation():
    """Test data segregation by server IP"""
    print("\n🔍 Testing Data Segregation...")
    try:
        from database.db_models import DatabaseOperations
        
        db_ops = DatabaseOperations()
        
        # Get all servers
        servers = db_ops.get_all_servers()
        
        print(f"📊 Testing data segregation for {len(servers)} servers:")
        
        segregation_working = True
        for server in servers:
            # Get metrics for this server
            metrics = db_ops.get_system_metrics_history(server.server_ip, 24)
            
            print(f"   {server.server_ip} ({server.hostname}): {len(metrics)} records")
            
            # Verify all records belong to this server
            for metric in metrics[:5]:  # Check first 5 records
                if metric.server_ip != server.server_ip:
                    print(f"   ❌ Data segregation error: Found {metric.server_ip} data in {server.server_ip} query")
                    segregation_working = False
                    break
        
        if segregation_working:
            print("✅ Data segregation: WORKING CORRECTLY")
            return True
        else:
            print("❌ Data segregation: ERRORS FOUND")
            return False
        
    except Exception as e:
        print(f"❌ Data segregation test error: {e}")
        return False

def main():
    """Run all Phase 3 tests"""
    print("🚀 PHASE 3 MULTI-SERVER DATA COLLECTION - TEST SUITE")
    print("=" * 65)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 65)
    
    tests = [
        ("Multi-Server Collector Initialization", test_multi_server_collector_initialization),
        ("Dashboard Integration", test_dashboard_integration),
        ("Server Discovery Integration", test_server_discovery_integration),
        ("Multi-Server Data Collection", test_multi_server_data_collection),
        ("API Endpoints", test_api_endpoints),
        ("Data Segregation", test_data_segregation)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: EXCEPTION - {e}")
            failed += 1
        
        time.sleep(1)  # Small delay between tests
    
    print("\n" + "=" * 65)
    print("📊 PHASE 3 TEST RESULTS")
    print("=" * 65)
    print(f"✅ Tests Passed: {passed}")
    print(f"❌ Tests Failed: {failed}")
    print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 PHASE 3 COMPLETED SUCCESSFULLY!")
        print("🚀 Multi-server data collection is fully functional!")
        print("📊 Your dashboard now:")
        print("   ✅ Collects data from multiple remote servers")
        print("   ✅ Stores all server data in centralized database")
        print("   ✅ Segregates data by server IP")
        print("   ✅ Provides unified multi-server APIs")
        print("   ✅ Maintains performance with parallel collection")
        print("\n🎯 READY FOR PRODUCTION MULTI-SERVER MONITORING!")
    elif failed <= 2:
        print(f"\n⚠️  PHASE 3 MOSTLY COMPLETE!")
        print(f"✅ Core functionality working with {failed} minor issue(s)")
        print("🔧 Review failed tests and proceed to production")
    else:
        print(f"\n❌ PHASE 3 NEEDS ATTENTION!")
        print(f"⚠️  {failed} critical issue(s) found")
        print("🔧 Please fix issues before production deployment")
    
    print("=" * 65)

if __name__ == "__main__":
    main()