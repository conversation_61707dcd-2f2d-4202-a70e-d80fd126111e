#!/usr/local/bin/python3.12
"""
Phase 5 Test Script - Production Deployment & Enterprise Features
Tests production configuration, security, performance optimization, and health monitoring
"""

import os
import sys
import time
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_production_configuration():
    """Test production configuration system"""
    print("🔍 Testing Production Configuration System...")
    try:
        from config.production_config import get_production_config, create_sample_configs
        
        # Test configuration loading
        config = get_production_config('production')
        
        print(f"✅ Production configuration loaded")
        print(f"   Environment: {config.environment}")
        print(f"   App Name: {config.app_name}")
        print(f"   App Version: {config.app_version}")
        print(f"   Port: {config.app_port}")
        print(f"   Debug Mode: {config.debug_mode}")
        
        # Test configuration validation
        validation = config.validate_config()
        print(f"   Configuration Valid: {validation['valid']}")
        
        if validation['errors']:
            print(f"   Errors: {len(validation['errors'])}")
            for error in validation['errors'][:3]:  # Show first 3 errors
                print(f"     - {error}")
        
        if validation['warnings']:
            print(f"   Warnings: {len(validation['warnings'])}")
            for warning in validation['warnings'][:2]:  # Show first 2 warnings
                print(f"     - {warning}")
        
        # Test configuration summary
        summary = config.get_config_summary()
        print(f"   Features enabled: {sum(1 for v in summary['features'].values() if v)}/{len(summary['features'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Production configuration test error: {e}")
        return False

def test_security_manager():
    """Test security and authentication system"""
    print("\n🔍 Testing Security & Authentication System...")
    try:
        from core.security_manager import SecurityManager
        
        security = SecurityManager()
        
        print(f"✅ Security manager initialized")
        print(f"   Default users created: {len(security.users)}")
        print(f"   Session timeout: {security.session_timeout}s")
        print(f"   Max failed attempts: {security.max_failed_attempts}")
        
        # Test user authentication
        auth_result = security.authenticate_user("admin", "admin123!", "127.0.0.1", "test-agent")
        
        if auth_result['success']:
            print(f"✅ Admin authentication successful")
            print(f"   Session ID: {auth_result['session']['session_id'][:16]}...")
            print(f"   User role: {auth_result['user']['role']}")
            
            # Test session validation
            session_info = security.validate_session(auth_result['session']['session_id'])
            if session_info:
                print(f"✅ Session validation successful")
                print(f"   Username: {session_info['username']}")
                print(f"   Role: {session_info['role']}")
        else:
            print(f"❌ Admin authentication failed: {auth_result['error']}")
        
        # Test API key creation
        api_key_result = security.create_api_key("admin", "test-key", ["read", "write"], 30)
        if api_key_result:
            print(f"✅ API key created")
            print(f"   Key ID: {api_key_result['key_id']}")
            print(f"   Permissions: {api_key_result['permissions']}")
            
            # Test API key validation
            api_validation = security.validate_api_key(api_key_result['api_key'])
            if api_validation:
                print(f"✅ API key validation successful")
                print(f"   Username: {api_validation['username']}")
        
        # Test security status
        status = security.get_security_status()
        print(f"✅ Security status:")
        print(f"   Total users: {status['total_users']}")
        print(f"   Active sessions: {status['active_sessions']}")
        print(f"   Active API keys: {status['active_api_keys']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Security manager test error: {e}")
        return False

def test_performance_optimizer():
    """Test performance optimization system"""
    print("\n🔍 Testing Performance Optimization System...")
    try:
        from core.performance_optimizer import PerformanceOptimizer
        
        optimizer = PerformanceOptimizer()
        
        print(f"✅ Performance optimizer initialized")
        
        # Test caching
        @optimizer.cached(ttl=60)
        def expensive_function(x):
            time.sleep(0.1)  # Simulate expensive operation
            return x * x
        
        # First call (cache miss)
        start_time = time.time()
        result1 = expensive_function(5)
        time1 = time.time() - start_time
        
        # Second call (cache hit)
        start_time = time.time()
        result2 = expensive_function(5)
        time2 = time.time() - start_time
        
        print(f"✅ Caching test:")
        print(f"   First call (cache miss): {time1:.3f}s")
        print(f"   Second call (cache hit): {time2:.3f}s")
        print(f"   Cache speedup: {time1/time2:.1f}x")
        
        # Test cache statistics
        cache_stats = optimizer.cache.get_stats()
        print(f"   Cache size: {cache_stats['size']}")
        print(f"   Hit rate: {cache_stats['hit_rate']:.2f}")
        
        # Test compression
        test_data = {"test": "data", "numbers": list(range(100))}
        compressed = optimizer.compress_response(test_data)
        decompressed = optimizer.decompress_request(compressed)
        
        print(f"✅ Compression test:")
        print(f"   Original data keys: {len(test_data)}")
        print(f"   Decompressed data keys: {len(decompressed)}")
        print(f"   Data integrity: {'✅ OK' if test_data == decompressed else '❌ FAILED'}")
        
        # Test performance stats
        perf_stats = optimizer.get_performance_stats()
        print(f"✅ Performance statistics:")
        print(f"   Cache hits: {perf_stats['cache']['hits']}")
        print(f"   Cache misses: {perf_stats['cache']['misses']}")
        print(f"   Thread pool size: {perf_stats['threads']['pool_size']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance optimizer test error: {e}")
        return False

def test_health_monitor():
    """Test system health monitoring"""
    print("\n🔍 Testing System Health Monitor...")
    try:
        from core.health_monitor import HealthMonitor
        
        health_monitor = HealthMonitor()
        
        print(f"✅ Health monitor initialized")
        print(f"   Check interval: {health_monitor.check_interval}s")
        print(f"   Health checkers: {len(health_monitor.health_checkers)}")
        
        # Test system health check
        health_status = health_monitor.check_system_health()
        
        print(f"✅ System health check completed:")
        print(f"   Overall status: {health_status.overall_status.upper()}")
        print(f"   Performance score: {health_status.performance_score:.1f}/100")
        print(f"   Uptime: {health_status.uptime:.0f} seconds")
        print(f"   Components checked: {len(health_status.components)}")
        
        # Show component status
        for component in health_status.components:
            status_icon = {
                'healthy': '✅',
                'warning': '⚠️',
                'critical': '❌',
                'unknown': '❓'
            }.get(component.status, '❓')
            
            print(f"   {status_icon} {component.component}: {component.status} - {component.message}")
            if component.response_time > 0:
                print(f"      Response time: {component.response_time:.3f}s")
        
        # Test health summary
        health_summary = health_monitor.get_health_summary()
        print(f"✅ Health summary:")
        print(f"   Current status: {health_summary['current_status']}")
        print(f"   Uptime: {health_summary['uptime_formatted']}")
        print(f"   Availability: {health_summary['availability_percent']:.1f}%")
        
        # Start monitoring briefly
        print(f"🏥 Starting health monitoring for 10 seconds...")
        health_monitor.start_monitoring()
        time.sleep(10)
        health_monitor.stop_monitoring()
        
        print(f"✅ Health monitoring test completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Health monitor test error: {e}")
        return False

def test_enterprise_integration():
    """Test enterprise features integration"""
    print("\n🔍 Testing Enterprise Features Integration...")
    try:
        # Test all systems working together
        from config.production_config import get_production_config
        from core.security_manager import get_security_manager
        from core.performance_optimizer import get_performance_optimizer
        from core.health_monitor import get_health_monitor
        
        config = get_production_config()
        security = get_security_manager()
        optimizer = get_performance_optimizer()
        health = get_health_monitor()
        
        print(f"✅ All enterprise systems initialized")
        
        # Test configuration integration
        print(f"   Configuration environment: {config.environment}")
        print(f"   Security authentication: {'Enabled' if config.security.authentication_enabled else 'Disabled'}")
        print(f"   Performance caching: {'Enabled' if config.performance.cache_enabled else 'Disabled'}")
        print(f"   Health monitoring: {'Enabled' if config.monitoring.self_monitoring_enabled else 'Disabled'}")
        
        # Test integrated functionality
        auth_result = security.authenticate_user("admin", "admin123!", "127.0.0.1")
        if auth_result['success']:
            print(f"✅ Integrated authentication working")
        
        # Test performance with health monitoring
        @optimizer.cached(ttl=30)
        def test_function():
            return {"timestamp": datetime.now().isoformat(), "data": "test"}
        
        result = test_function()
        print(f"✅ Integrated caching working")
        
        # Check system health
        health_status = health.check_system_health()
        print(f"✅ Integrated health monitoring working")
        print(f"   System status: {health_status.overall_status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enterprise integration test error: {e}")
        return False

def test_production_readiness():
    """Test production readiness"""
    print("\n🔍 Testing Production Readiness...")
    try:
        from config.production_config import get_production_config
        
        config = get_production_config('production')
        
        print(f"✅ Production readiness check:")
        
        # Check critical production settings
        production_checks = {
            'Debug mode disabled': not config.debug_mode,
            'Authentication enabled': config.security.authentication_enabled,
            'SSL configured': config.security.ssl_enabled,
            'Database backup enabled': config.database.backup_enabled,
            'Performance caching enabled': config.performance.cache_enabled,
            'System backup enabled': config.backup.enabled,
            'Structured logging enabled': config.logging.structured_logging,
            'Health monitoring enabled': config.monitoring.self_monitoring_enabled
        }
        
        passed_checks = 0
        for check_name, passed in production_checks.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"   {status} {check_name}")
            if passed:
                passed_checks += 1
        
        readiness_score = (passed_checks / len(production_checks)) * 100
        print(f"\n📊 Production Readiness Score: {readiness_score:.1f}%")
        
        if readiness_score >= 90:
            print(f"🎉 PRODUCTION READY!")
        elif readiness_score >= 75:
            print(f"⚠️  MOSTLY READY - Address failed checks")
        else:
            print(f"❌ NOT READY - Critical issues need attention")
        
        return readiness_score >= 75
        
    except Exception as e:
        print(f"❌ Production readiness test error: {e}")
        return False

def main():
    """Run all Phase 5 tests"""
    print("🚀 PHASE 5 PRODUCTION DEPLOYMENT & ENTERPRISE FEATURES - TEST SUITE")
    print("=" * 80)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    tests = [
        ("Production Configuration", test_production_configuration),
        ("Security & Authentication", test_security_manager),
        ("Performance Optimization", test_performance_optimizer),
        ("System Health Monitor", test_health_monitor),
        ("Enterprise Integration", test_enterprise_integration),
        ("Production Readiness", test_production_readiness)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: EXCEPTION - {e}")
            failed += 1
        
        time.sleep(1)  # Small delay between tests
    
    print("\n" + "=" * 80)
    print("📊 PHASE 5 TEST RESULTS")
    print("=" * 80)
    print(f"✅ Tests Passed: {passed}")
    print(f"❌ Tests Failed: {failed}")
    print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 PHASE 5 COMPLETED SUCCESSFULLY!")
        print("🚀 Enterprise-grade production deployment features are fully functional!")
        print("📊 Your PinnacleAi Resource Manager now has:")
        print("   ✅ Production configuration management")
        print("   ✅ Enterprise security & authentication")
        print("   ✅ Advanced performance optimization")
        print("   ✅ System health monitoring & self-diagnostics")
        print("   ✅ Production-ready deployment features")
        print("   ✅ Enterprise-grade reliability & scalability")
        print("\n🏆 ENTERPRISE MONITORING PLATFORM COMPLETE!")
        print("🎯 READY FOR ENTERPRISE PRODUCTION DEPLOYMENT!")
    elif failed <= 2:
        print(f"\n⚠️  PHASE 5 MOSTLY COMPLETE!")
        print(f"✅ Core enterprise functionality working with {failed} minor issue(s)")
        print("🔧 Review failed tests and proceed to production")
    else:
        print(f"\n❌ PHASE 5 NEEDS ATTENTION!")
        print(f"⚠️  {failed} critical issue(s) found")
        print("🔧 Please fix issues before production deployment")
    
    print("=" * 80)

if __name__ == "__main__":
    main()