#!/usr/bin/env python3
"""
Simple test server to verify basic functionality
"""

import json
import psutil
from http.server import HTTPServer, BaseHTTPRequestHandler
from datetime import datetime

class TestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/api/test':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            data = {
                'status': 'working',
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'timestamp': datetime.now().isoformat()
            }
            
            self.wfile.write(json.dumps(data).encode('utf-8'))
        else:
            self.send_error(404, "Not Found")

if __name__ == "__main__":
    server = HTTPServer(('0.0.0.0', 8005), TestHandler)
    print("🧪 Test server started on http://localhost:8005")
    print("📊 Test endpoint: http://localhost:8005/api/test")
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Test server stopped")
        server.shutdown()