#!/usr/local/bin/python3.12
"""
Phase 3 Demo - Multi-Server Data Collection
Demonstrates multi-server functionality with simulated remote servers
"""

import os
import sys
import time
import json
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_multi_server_architecture():
    """Demonstrate multi-server architecture"""
    print("🚀 PHASE 3 MULTI-SERVER ARCHITECTURE DEMO")
    print("=" * 55)
    print(f"📅 Demo Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 55)
    
    try:
        from database.db_models import DatabaseOperations
        from web_dashboard import DashboardData
        
        # Initialize components
        db_ops = DatabaseOperations()
        dashboard = DashboardData()
        
        print("🔍 CURRENT MULTI-SERVER STATUS:")
        print(f"   Database Integration: {'✅ ENABLED' if dashboard.database_enabled else '❌ DISABLED'}")
        print(f"   Multi-Server Collection: {'✅ ENABLED' if dashboard.multi_server_enabled else '❌ DISABLED'}")
        
        # Show all registered servers
        servers = db_ops.get_all_servers()
        print(f"\n📊 REGISTERED SERVERS ({len(servers)} total):")
        
        local_servers = 0
        remote_servers = 0
        
        for server in servers:
            # Get latest metrics
            latest_metrics = db_ops.get_latest_system_metrics(server.server_ip)
            
            if latest_metrics:
                is_remote = latest_metrics.is_remote
                age = datetime.now() - latest_metrics.timestamp
                age_str = f"{age.total_seconds():.0f}s ago"
                
                server_type = "🌐 REMOTE" if is_remote else "🏠 LOCAL"
                if is_remote:
                    remote_servers += 1
                else:
                    local_servers += 1
                
                print(f"   {server_type} {server.server_ip} ({server.hostname})")
                print(f"      Status: {server.status}")
                print(f"      Latest Data: {age_str}")
                print(f"      CPU: {latest_metrics.cpu_percent}%, Memory: {latest_metrics.memory_percent}%")
                print(f"      OS: {server.os_info}")
            else:
                print(f"   📝 REGISTERED {server.server_ip} ({server.hostname})")
                print(f"      Status: {server.status}")
                print(f"      Latest Data: No data available")
                print(f"      OS: {server.os_info}")
            print()
        
        print(f"📈 SERVER SUMMARY:")
        print(f"   Local Servers with Data: {local_servers}")
        print(f"   Remote Servers with Data: {remote_servers}")
        print(f"   Total Registered: {len(servers)}")
        
        # Show database statistics
        stats = db_ops.get_database_stats()
        print(f"\n📊 DATABASE STATISTICS:")
        for table, count in stats.items():
            print(f"   {table}: {count:,} records")
        
        # Show data collection status
        if dashboard.multi_server_enabled and dashboard.multi_server_collector:
            status = dashboard.multi_server_collector.get_collection_status()
            print(f"\n🔄 MULTI-SERVER COLLECTION STATUS:")
            print(f"   Status: {status['status'].upper()}")
            print(f"   Collection Interval: {status['interval']} seconds")
            print(f"   Connected Servers: {status['servers']}")
            print(f"   Server IPs: {status['connected_servers']}")
        
        # Show recent activity
        print(f"\n📈 RECENT DATA ACTIVITY:")
        recent_data = {}
        for server in servers:
            metrics_history = db_ops.get_system_metrics_history(server.server_ip, 1)  # Last 1 hour
            if metrics_history:
                recent_data[server.server_ip] = len(metrics_history)
        
        for server_ip, count in recent_data.items():
            server_name = next((s.hostname for s in servers if s.server_ip == server_ip), server_ip)
            print(f"   {server_ip} ({server_name}): {count} records in last hour")
        
        print(f"\n🎯 PHASE 3 CAPABILITIES DEMONSTRATED:")
        print(f"   ✅ Multi-server database architecture")
        print(f"   ✅ Server registration and management")
        print(f"   ✅ Data segregation by server IP")
        print(f"   ✅ Local and remote server differentiation")
        print(f"   ✅ Centralized data storage")
        print(f"   ✅ Historical data preservation")
        print(f"   ✅ Multi-server API endpoints")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo error: {e}")
        return False

def demo_api_endpoints():
    """Demonstrate API endpoints"""
    print(f"\n🌐 API ENDPOINTS DEMONSTRATION:")
    print(f"   Your dashboard now provides these multi-server APIs:")
    print()
    
    print(f"   📊 Database Statistics:")
    print(f"      GET /api/database-stats")
    print(f"      Returns: Server count, metrics count, server list")
    print()
    
    print(f"   🔄 Multi-Server Status:")
    print(f"      GET /api/multi-server-status")
    print(f"      Returns: Collection status, connected servers")
    print()
    
    print(f"   🌐 All Servers Data:")
    print(f"      GET /api/all-servers-data")
    print(f"      Returns: Latest metrics from all registered servers")
    print()
    
    print(f"   📈 Historical Data:")
    print(f"      GET /api/historical-data?server_ip=X.X.X.X&hours=24")
    print(f"      Returns: Time-series data for specific server")
    print()

def demo_data_flow():
    """Demonstrate data flow architecture"""
    print(f"\n🔄 MULTI-SERVER DATA FLOW ARCHITECTURE:")
    print()
    
    print(f"   LOCAL SERVER (************):")
    print(f"   ├── Every 5 seconds:")
    print(f"   │   ├── Collect local metrics")
    print(f"   │   ├── Store in PostgreSQL (is_remote=False)")
    print(f"   │   └── Update dashboard display")
    print(f"   └── Database: system_metrics, process_metrics, network_metrics, disk_metrics")
    print()
    
    print(f"   REMOTE SERVERS (via SSH):")
    print(f"   ├── Every 30 seconds:")
    print(f"   │   ├── SSH to each connected server")
    print(f"   │   ├── Execute system commands remotely")
    print(f"   │   ├── Collect metrics (CPU, memory, disk, processes)")
    print(f"   │   └── Store in PostgreSQL (is_remote=True)")
    print(f"   └── Database: Same tables, segregated by server_ip")
    print()
    
    print(f"   CENTRALIZED DATABASE:")
    print(f"   ├── PostgreSQL stores ALL server data")
    print(f"   ├── Data segregated by server_ip column")
    print(f"   ├── Local and remote data differentiated")
    print(f"   └── Historical data preserved for analysis")
    print()

def demo_production_readiness():
    """Demonstrate production readiness"""
    print(f"\n🏭 PRODUCTION READINESS FEATURES:")
    print()
    
    print(f"   ✅ SCALABILITY:")
    print(f"      • Supports unlimited remote servers")
    print(f"      • Parallel data collection with thread pools")
    print(f"      • Non-blocking database operations")
    print(f"      • Connection pooling for efficiency")
    print()
    
    print(f"   ✅ RELIABILITY:")
    print(f"      • Error handling for SSH failures")
    print(f"      • Graceful degradation if servers unavailable")
    print(f"      • Automatic retry mechanisms")
    print(f"      • Server status tracking")
    print()
    
    print(f"   ✅ PERFORMANCE:")
    print(f"      • Local data: 5-second intervals")
    print(f"      • Remote data: 30-second intervals (SSH optimized)")
    print(f"      • Background processing")
    print(f"      • Efficient database queries")
    print()
    
    print(f"   ✅ MONITORING:")
    print(f"      • Real-time server status")
    print(f"      • Data collection health monitoring")
    print(f"      • Historical trend analysis")
    print(f"      • Alert capabilities (ready for expansion)")
    print()

def main():
    """Run Phase 3 demonstration"""
    success = demo_multi_server_architecture()
    
    if success:
        demo_api_endpoints()
        demo_data_flow()
        demo_production_readiness()
        
        print("\n" + "=" * 55)
        print("🎉 PHASE 3 MULTI-SERVER ARCHITECTURE COMPLETE!")
        print("=" * 55)
        print("🚀 Your PinnacleAi Resource Manager now supports:")
        print("   • Multi-server data collection")
        print("   • Centralized PostgreSQL storage")
        print("   • Local + Remote server monitoring")
        print("   • Historical data analysis")
        print("   • Production-ready scalability")
        print()
        print("🎯 READY FOR ENTERPRISE DEPLOYMENT!")
        print("=" * 55)
    else:
        print("\n❌ Demo failed - please check configuration")

if __name__ == "__main__":
    main()