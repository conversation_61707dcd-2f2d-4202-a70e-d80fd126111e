alerting:
  alert_cooldown: 300
  email_enabled: true
  email_from: <EMAIL>
  email_to: null
  sms_enabled: false
  smtp_password: ''
  smtp_port: 587
  smtp_server: smtp.gmail.com
  smtp_username: ''
  webhook_enabled: false
  webhook_url: ''
application:
  debug: false
  environment: staging
  host: 0.0.0.0
  maintenance: false
  name: PinnacleAi Resource Manager
  port: 8005
  version: 1.0.0
backup:
  backup_path: /var/backups/pinnacle
  compression: true
  enabled: true
  encryption: true
  remote_backup: false
  remote_path: ''
  retention_days: 30
  schedule: 0 2 * * *
database:
  backup_enabled: true
  backup_schedule: 0 2 * * *
  database: resource_monitor
  host: localhost
  max_overflow: 30
  password: secure_password_2024
  pool_recycle: 3600
  pool_size: 10
  pool_timeout: 30
  port: 5432
  ssl_mode: prefer
  username: resource_user
logging:
  backup_count: 5
  console_enabled: true
  file_enabled: true
  level: INFO
  log_file: /var/log/pinnacle/pinnacle.log
  log_rotation: true
  max_file_size: 100
  structured_logging: true
monitoring:
  alert_check_interval: 60
  alert_retention_days: 30
  data_collection_interval: 5
  health_check_interval: 30
  metrics_retention_days: 90
  multi_server_interval: 30
  performance_baseline_days: 7
  self_monitoring_enabled: true
performance:
  async_processing: true
  batch_size: 100
  cache_enabled: true
  cache_max_size: 1000
  cache_ttl: 300
  compression_enabled: true
  connection_pool_size: 20
  query_timeout: 30
  thread_pool_size: 10
security:
  allowed_origins: null
  api_key_enabled: true
  authentication_enabled: true
  cors_enabled: false
  lockout_duration: 900
  max_login_attempts: 5
  password_min_length: 8
  password_require_special: true
  session_timeout: 3600
  ssl_cert_path: /etc/ssl/certs/pinnacle.crt
  ssl_enabled: true
  ssl_key_path: /etc/ssl/private/pinnacle.key
