#!/usr/bin/env python3.12
"""
Production Configuration Management - Phase 5
Environment-specific configuration management for production deployment
"""

import os
import sys
import json
import yaml
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import logging

class Environment(Enum):
    """Environment types"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"

class LogLevel(Enum):
    """Log levels"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

@dataclass
class DatabaseConfig:
    """Database configuration"""
    host: str = "localhost"
    port: int = 5432
    database: str = "resource_monitor"
    username: str = "resource_user"
    password: str = "secure_password_2024"
    pool_size: int = 20
    max_overflow: int = 30
    pool_timeout: int = 30
    pool_recycle: int = 3600
    ssl_mode: str = "prefer"
    backup_enabled: bool = True
    backup_schedule: str = "0 2 * * *"  # Daily at 2 AM

@dataclass
class SecurityConfig:
    """Security configuration"""
    authentication_enabled: bool = True
    session_timeout: int = 3600  # 1 hour
    max_login_attempts: int = 5
    lockout_duration: int = 900  # 15 minutes
    password_min_length: int = 8
    password_require_special: bool = True
    ssl_enabled: bool = True
    ssl_cert_path: str = "/etc/ssl/certs/pinnacle.crt"
    ssl_key_path: str = "/etc/ssl/private/pinnacle.key"
    api_key_enabled: bool = True
    cors_enabled: bool = False
    allowed_origins: List[str] = None

@dataclass
class MonitoringConfig:
    """Monitoring configuration"""
    data_collection_interval: int = 5  # seconds
    alert_check_interval: int = 60  # seconds
    multi_server_interval: int = 30  # seconds
    metrics_retention_days: int = 90
    alert_retention_days: int = 30
    performance_baseline_days: int = 7
    health_check_interval: int = 30  # seconds
    self_monitoring_enabled: bool = True

@dataclass
class PerformanceConfig:
    """Performance configuration"""
    cache_enabled: bool = True
    cache_ttl: int = 300  # 5 minutes
    cache_max_size: int = 1000
    compression_enabled: bool = True
    async_processing: bool = True
    thread_pool_size: int = 10
    connection_pool_size: int = 20
    query_timeout: int = 30
    batch_size: int = 100

@dataclass
class AlertingConfig:
    """Alerting configuration"""
    email_enabled: bool = True
    smtp_server: str = "smtp.gmail.com"
    smtp_port: int = 587
    smtp_username: str = ""
    smtp_password: str = ""
    email_from: str = "<EMAIL>"
    email_to: List[str] = None
    sms_enabled: bool = False
    webhook_enabled: bool = False
    webhook_url: str = ""
    alert_cooldown: int = 300  # 5 minutes between same alerts

@dataclass
class BackupConfig:
    """Backup configuration"""
    enabled: bool = True
    backup_path: str = "/var/backups/pinnacle"
    retention_days: int = 30
    compression: bool = True
    encryption: bool = True
    remote_backup: bool = False
    remote_path: str = ""
    schedule: str = "0 2 * * *"  # Daily at 2 AM

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = LogLevel.INFO.value
    file_enabled: bool = True
    log_file: str = "/var/log/pinnacle/pinnacle.log"
    max_file_size: int = 100  # MB
    backup_count: int = 5
    console_enabled: bool = True
    structured_logging: bool = True
    log_rotation: bool = True

class ProductionConfig:
    """Production configuration manager"""
    
    def __init__(self, environment: str = Environment.PRODUCTION.value):
        self.environment = environment
        self.config_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)))
        self.config_file = os.path.join(self.config_dir, f"{environment}.yaml")
        
        # Default configurations
        self.database = DatabaseConfig()
        self.security = SecurityConfig()
        self.monitoring = MonitoringConfig()
        self.performance = PerformanceConfig()
        self.alerting = AlertingConfig()
        self.backup = BackupConfig()
        self.logging = LoggingConfig()
        
        # Application settings
        self.app_name = "PinnacleAi Resource Manager"
        self.app_version = "1.0.0"
        self.app_port = 8005
        self.app_host = "0.0.0.0"
        self.debug_mode = False
        self.maintenance_mode = False
        
        # Load configuration
        self.load_config()
        
        # Setup logging
        self.setup_logging()
    
    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config_data = yaml.safe_load(f)
                
                # Update configurations from file
                if 'database' in config_data:
                    self._update_dataclass(self.database, config_data['database'])
                
                if 'security' in config_data:
                    self._update_dataclass(self.security, config_data['security'])
                
                if 'monitoring' in config_data:
                    self._update_dataclass(self.monitoring, config_data['monitoring'])
                
                if 'performance' in config_data:
                    self._update_dataclass(self.performance, config_data['performance'])
                
                if 'alerting' in config_data:
                    self._update_dataclass(self.alerting, config_data['alerting'])
                
                if 'backup' in config_data:
                    self._update_dataclass(self.backup, config_data['backup'])
                
                if 'logging' in config_data:
                    self._update_dataclass(self.logging, config_data['logging'])
                
                # Update app settings
                if 'application' in config_data:
                    app_config = config_data['application']
                    self.app_port = app_config.get('port', self.app_port)
                    self.app_host = app_config.get('host', self.app_host)
                    self.debug_mode = app_config.get('debug', self.debug_mode)
                    self.maintenance_mode = app_config.get('maintenance', self.maintenance_mode)
                
                print(f"✅ Configuration loaded from {self.config_file}")
            else:
                print(f"📄 No configuration file found: {self.config_file}")
                print("📝 Using default configuration")
                self.save_config()  # Save default config
                
        except Exception as e:
            print(f"❌ Error loading configuration: {e}")
            print("📝 Using default configuration")
    
    def save_config(self):
        """Save current configuration to file"""
        try:
            # Ensure config directory exists
            os.makedirs(self.config_dir, exist_ok=True)
            
            config_data = {
                'application': {
                    'name': self.app_name,
                    'version': self.app_version,
                    'environment': self.environment,
                    'port': self.app_port,
                    'host': self.app_host,
                    'debug': self.debug_mode,
                    'maintenance': self.maintenance_mode
                },
                'database': asdict(self.database),
                'security': asdict(self.security),
                'monitoring': asdict(self.monitoring),
                'performance': asdict(self.performance),
                'alerting': asdict(self.alerting),
                'backup': asdict(self.backup),
                'logging': asdict(self.logging)
            }
            
            with open(self.config_file, 'w') as f:
                yaml.dump(config_data, f, default_flow_style=False, indent=2)
            
            print(f"✅ Configuration saved to {self.config_file}")
            return True
            
        except Exception as e:
            print(f"❌ Error saving configuration: {e}")
            return False
    
    def _update_dataclass(self, dataclass_instance, config_dict):
        """Update dataclass instance with values from dictionary"""
        for key, value in config_dict.items():
            if hasattr(dataclass_instance, key):
                setattr(dataclass_instance, key, value)
    
    def setup_logging(self):
        """Setup logging based on configuration"""
        try:
            # Create log directory if it doesn't exist
            if self.logging.file_enabled:
                log_dir = os.path.dirname(self.logging.log_file)
                os.makedirs(log_dir, exist_ok=True)
            
            # Configure logging
            log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            if self.logging.structured_logging:
                log_format = '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
            
            logging_config = {
                'level': getattr(logging, self.logging.level),
                'format': log_format,
                'handlers': []
            }
            
            # Console handler
            if self.logging.console_enabled:
                console_handler = logging.StreamHandler()
                console_handler.setFormatter(logging.Formatter(log_format))
                logging_config['handlers'].append(console_handler)
            
            # File handler
            if self.logging.file_enabled:
                if self.logging.log_rotation:
                    from logging.handlers import RotatingFileHandler
                    file_handler = RotatingFileHandler(
                        self.logging.log_file,
                        maxBytes=self.logging.max_file_size * 1024 * 1024,
                        backupCount=self.logging.backup_count
                    )
                else:
                    file_handler = logging.FileHandler(self.logging.log_file)
                
                file_handler.setFormatter(logging.Formatter(log_format))
                logging_config['handlers'].append(file_handler)
            
            # Apply logging configuration
            logging.basicConfig(**logging_config)
            
            print(f"✅ Logging configured: level={self.logging.level}, file={self.logging.file_enabled}")
            
        except Exception as e:
            print(f"❌ Error setting up logging: {e}")
    
    def get_database_url(self):
        """Get database connection URL"""
        return (f"postgresql://{self.database.username}:{self.database.password}@"
                f"{self.database.host}:{self.database.port}/{self.database.database}")
    
    def is_production(self):
        """Check if running in production environment"""
        return self.environment == Environment.PRODUCTION.value
    
    def is_development(self):
        """Check if running in development environment"""
        return self.environment == Environment.DEVELOPMENT.value
    
    def validate_config(self):
        """Validate configuration settings"""
        errors = []
        warnings = []
        
        # Database validation
        if not self.database.host:
            errors.append("Database host is required")
        
        if not self.database.username:
            errors.append("Database username is required")
        
        # Security validation
        if self.security.authentication_enabled and self.security.session_timeout < 300:
            warnings.append("Session timeout is very short (< 5 minutes)")
        
        if self.security.ssl_enabled:
            if not os.path.exists(self.security.ssl_cert_path):
                errors.append(f"SSL certificate not found: {self.security.ssl_cert_path}")
            
            if not os.path.exists(self.security.ssl_key_path):
                errors.append(f"SSL key not found: {self.security.ssl_key_path}")
        
        # Performance validation
        if self.performance.thread_pool_size > 50:
            warnings.append("Thread pool size is very high (> 50)")
        
        # Alerting validation
        if self.alerting.email_enabled:
            if not self.alerting.smtp_server:
                errors.append("SMTP server is required for email alerts")
            
            if not self.alerting.email_to:
                warnings.append("No email recipients configured for alerts")
        
        # Backup validation
        if self.backup.enabled:
            backup_dir = os.path.dirname(self.backup.backup_path)
            if not os.path.exists(backup_dir):
                try:
                    os.makedirs(backup_dir, exist_ok=True)
                except Exception:
                    errors.append(f"Cannot create backup directory: {backup_dir}")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    def get_config_summary(self):
        """Get configuration summary"""
        return {
            'application': {
                'name': self.app_name,
                'version': self.app_version,
                'environment': self.environment,
                'port': self.app_port,
                'debug': self.debug_mode
            },
            'features': {
                'database': True,
                'security': self.security.authentication_enabled,
                'ssl': self.security.ssl_enabled,
                'caching': self.performance.cache_enabled,
                'alerting': self.alerting.email_enabled,
                'backup': self.backup.enabled,
                'monitoring': self.monitoring.self_monitoring_enabled
            },
            'performance': {
                'cache_ttl': self.performance.cache_ttl,
                'thread_pool': self.performance.thread_pool_size,
                'connection_pool': self.performance.connection_pool_size
            }
        }

# Global configuration instance
production_config = None

def get_production_config(environment: str = None):
    """Get global production configuration instance"""
    global production_config
    if production_config is None:
        env = environment or os.getenv('PINNACLE_ENV', Environment.PRODUCTION.value)
        production_config = ProductionConfig(env)
    return production_config

def create_sample_configs():
    """Create sample configuration files for all environments"""
    environments = [Environment.DEVELOPMENT.value, Environment.STAGING.value, Environment.PRODUCTION.value]
    
    for env in environments:
        config = ProductionConfig(env)
        
        # Customize for environment
        if env == Environment.DEVELOPMENT.value:
            config.debug_mode = True
            config.security.authentication_enabled = False
            config.security.ssl_enabled = False
            config.logging.level = LogLevel.DEBUG.value
            config.monitoring.data_collection_interval = 10
            config.database.pool_size = 5
        
        elif env == Environment.STAGING.value:
            config.debug_mode = False
            config.security.authentication_enabled = True
            config.security.ssl_enabled = True
            config.logging.level = LogLevel.INFO.value
            config.monitoring.data_collection_interval = 5
            config.database.pool_size = 10
        
        elif env == Environment.PRODUCTION.value:
            config.debug_mode = False
            config.security.authentication_enabled = True
            config.security.ssl_enabled = True
            config.logging.level = LogLevel.WARNING.value
            config.monitoring.data_collection_interval = 5
            config.database.pool_size = 20
            config.performance.cache_enabled = True
            config.backup.enabled = True
        
        config.save_config()
        print(f"✅ Created {env} configuration")

if __name__ == "__main__":
    # Create sample configurations
    create_sample_configs()
    
    # Test configuration loading
    config = get_production_config()
    validation = config.validate_config()
    
    print(f"\n📊 Configuration Summary:")
    summary = config.get_config_summary()
    for section, data in summary.items():
        print(f"  {section}: {data}")
    
    print(f"\n🔍 Configuration Validation:")
    print(f"  Valid: {validation['valid']}")
    if validation['errors']:
        print(f"  Errors: {validation['errors']}")
    if validation['warnings']:
        print(f"  Warnings: {validation['warnings']}")