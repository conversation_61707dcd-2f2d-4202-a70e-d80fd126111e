#!/usr/bin/env python3.12
"""
Database Configuration for PinnacleAi Resource Manager
Contains all database-related settings and configurations
"""

import os
from typing import Dict, Any

class DatabaseConfig:
    """Database configuration settings"""
    
    # Database Connection Settings
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_PORT = int(os.getenv('DB_PORT', 5432))
    DB_NAME = os.getenv('DB_NAME', 'resource_monitor')
    DB_USER = os.getenv('DB_USER', 'resource_user')
    DB_PASSWORD = os.getenv('DB_PASSWORD', 'pinnacle_resource_2025')
    
    # Connection Pool Settings
    DB_MIN_CONNECTIONS = int(os.getenv('DB_MIN_CONN', 2))
    DB_MAX_CONNECTIONS = int(os.getenv('DB_MAX_CONN', 10))
    DB_CONNECTION_TIMEOUT = int(os.getenv('DB_TIMEOUT', 30))
    
    # Data Retention Settings (in days)
    DETAILED_DATA_RETENTION_DAYS = int(os.getenv('DETAILED_RETENTION_DAYS', 30))
    HOURLY_SUMMARY_RETENTION_DAYS = int(os.getenv('HOURLY_RETENTION_DAYS', 365))
    DAILY_SUMMARY_RETENTION_DAYS = int(os.getenv('DAILY_RETENTION_DAYS', 1825))  # 5 years
    
    # Performance Settings
    BATCH_INSERT_SIZE = int(os.getenv('BATCH_INSERT_SIZE', 100))
    QUERY_TIMEOUT = int(os.getenv('QUERY_TIMEOUT', 30))
    
    # Monitoring Settings
    ENABLE_QUERY_LOGGING = os.getenv('ENABLE_QUERY_LOGGING', 'false').lower() == 'true'
    SLOW_QUERY_THRESHOLD = float(os.getenv('SLOW_QUERY_THRESHOLD', 1.0))  # seconds
    
    @classmethod
    def get_connection_config(cls) -> Dict[str, Any]:
        """Get database connection configuration"""
        return {
            'host': cls.DB_HOST,
            'port': cls.DB_PORT,
            'database': cls.DB_NAME,
            'user': cls.DB_USER,
            'password': cls.DB_PASSWORD,
            'minconn': cls.DB_MIN_CONNECTIONS,
            'maxconn': cls.DB_MAX_CONNECTIONS,
            'connect_timeout': cls.DB_CONNECTION_TIMEOUT
        }
    
    @classmethod
    def get_retention_config(cls) -> Dict[str, int]:
        """Get data retention configuration"""
        return {
            'detailed_data_days': cls.DETAILED_DATA_RETENTION_DAYS,
            'hourly_summary_days': cls.HOURLY_SUMMARY_RETENTION_DAYS,
            'daily_summary_days': cls.DAILY_SUMMARY_RETENTION_DAYS
        }
    
    @classmethod
    def get_performance_config(cls) -> Dict[str, Any]:
        """Get performance configuration"""
        return {
            'batch_insert_size': cls.BATCH_INSERT_SIZE,
            'query_timeout': cls.QUERY_TIMEOUT,
            'enable_query_logging': cls.ENABLE_QUERY_LOGGING,
            'slow_query_threshold': cls.SLOW_QUERY_THRESHOLD
        }

class AlertThresholds:
    """Alert threshold configurations"""
    
    # CPU Thresholds
    CPU_WARNING_THRESHOLD = float(os.getenv('CPU_WARNING_THRESHOLD', 70.0))
    CPU_CRITICAL_THRESHOLD = float(os.getenv('CPU_CRITICAL_THRESHOLD', 90.0))
    
    # Memory Thresholds
    MEMORY_WARNING_THRESHOLD = float(os.getenv('MEMORY_WARNING_THRESHOLD', 80.0))
    MEMORY_CRITICAL_THRESHOLD = float(os.getenv('MEMORY_CRITICAL_THRESHOLD', 95.0))
    
    # Disk Thresholds
    DISK_WARNING_THRESHOLD = float(os.getenv('DISK_WARNING_THRESHOLD', 80.0))
    DISK_CRITICAL_THRESHOLD = float(os.getenv('DISK_CRITICAL_THRESHOLD', 95.0))
    
    # Load Average Thresholds (per CPU core)
    LOAD_WARNING_THRESHOLD = float(os.getenv('LOAD_WARNING_THRESHOLD', 1.5))
    LOAD_CRITICAL_THRESHOLD = float(os.getenv('LOAD_CRITICAL_THRESHOLD', 2.0))
    
    # Network Thresholds (bytes per second)
    NETWORK_WARNING_THRESHOLD = int(os.getenv('NETWORK_WARNING_THRESHOLD', 100000000))  # 100MB/s
    NETWORK_CRITICAL_THRESHOLD = int(os.getenv('NETWORK_CRITICAL_THRESHOLD', 500000000))  # 500MB/s
    
    @classmethod
    def get_all_thresholds(cls) -> Dict[str, Dict[str, float]]:
        """Get all alert thresholds"""
        return {
            'cpu': {
                'warning': cls.CPU_WARNING_THRESHOLD,
                'critical': cls.CPU_CRITICAL_THRESHOLD
            },
            'memory': {
                'warning': cls.MEMORY_WARNING_THRESHOLD,
                'critical': cls.MEMORY_CRITICAL_THRESHOLD
            },
            'disk': {
                'warning': cls.DISK_WARNING_THRESHOLD,
                'critical': cls.DISK_CRITICAL_THRESHOLD
            },
            'load': {
                'warning': cls.LOAD_WARNING_THRESHOLD,
                'critical': cls.LOAD_CRITICAL_THRESHOLD
            },
            'network': {
                'warning': cls.NETWORK_WARNING_THRESHOLD,
                'critical': cls.NETWORK_CRITICAL_THRESHOLD
            }
        }

class DatabaseTables:
    """Database table names and schemas"""
    
    # Table Names
    SERVERS = 'servers'
    SYSTEM_METRICS = 'system_metrics'
    PROCESS_METRICS = 'process_metrics'
    NETWORK_METRICS = 'network_metrics'
    DISK_METRICS = 'disk_metrics'
    SERVER_EVENTS = 'server_events'
    ALERTS_LOG = 'alerts_log'
    
    # View Names
    LATEST_SERVER_METRICS = 'latest_server_metrics'
    SERVER_SUMMARY = 'server_summary'
    
    @classmethod
    def get_all_tables(cls) -> list:
        """Get list of all table names"""
        return [
            cls.SERVERS,
            cls.SYSTEM_METRICS,
            cls.PROCESS_METRICS,
            cls.NETWORK_METRICS,
            cls.DISK_METRICS,
            cls.SERVER_EVENTS,
            cls.ALERTS_LOG
        ]
    
    @classmethod
    def get_all_views(cls) -> list:
        """Get list of all view names"""
        return [
            cls.LATEST_SERVER_METRICS,
            cls.SERVER_SUMMARY
        ]

# Environment-specific configurations
class EnvironmentConfig:
    """Environment-specific database configurations"""
    
    DEVELOPMENT = {
        'db_name': 'resource_monitor_dev',
        'log_level': 'DEBUG',
        'enable_query_logging': True,
        'retention_days': 7
    }
    
    STAGING = {
        'db_name': 'resource_monitor_staging',
        'log_level': 'INFO',
        'enable_query_logging': True,
        'retention_days': 30
    }
    
    PRODUCTION = {
        'db_name': 'resource_monitor',
        'log_level': 'WARNING',
        'enable_query_logging': False,
        'retention_days': 90
    }
    
    @classmethod
    def get_config(cls, environment: str = 'production') -> Dict[str, Any]:
        """Get configuration for specific environment"""
        env_configs = {
            'development': cls.DEVELOPMENT,
            'staging': cls.STAGING,
            'production': cls.PRODUCTION
        }
        return env_configs.get(environment.lower(), cls.PRODUCTION)

# Export main configuration
def get_database_config() -> Dict[str, Any]:
    """Get complete database configuration"""
    return {
        'connection': DatabaseConfig.get_connection_config(),
        'retention': DatabaseConfig.get_retention_config(),
        'performance': DatabaseConfig.get_performance_config(),
        'thresholds': AlertThresholds.get_all_thresholds(),
        'tables': DatabaseTables.get_all_tables(),
        'views': DatabaseTables.get_all_views()
    }

if __name__ == "__main__":
    # Test configuration
    print("🔍 Database Configuration Test")
    print("=" * 50)
    
    config = get_database_config()
    
    print("📊 Connection Config:")
    for key, value in config['connection'].items():
        if key == 'password':
            print(f"   {key}: {'*' * len(str(value))}")
        else:
            print(f"   {key}: {value}")
    
    print("\n⏰ Retention Config:")
    for key, value in config['retention'].items():
        print(f"   {key}: {value} days")
    
    print("\n🚨 Alert Thresholds:")
    for metric, thresholds in config['thresholds'].items():
        print(f"   {metric.upper()}:")
        for level, value in thresholds.items():
            print(f"     {level}: {value}")
    
    print("\n📋 Database Tables:")
    for table in config['tables']:
        print(f"   - {table}")
    
    print("\n👁️ Database Views:")
    for view in config['views']:
        print(f"   - {view}")
    
    print("\n✅ Configuration test completed!")