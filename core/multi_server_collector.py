#!/usr/bin/env python3.12
"""
Multi-Server Data Collector - Phase 3
Collects metrics from multiple remote servers and stores in centralized database
"""

import os
import sys
import time
import json
import threading
import subprocess
import socket
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from database.db_models import DatabaseOperations, Server, SystemMetrics, ProcessMetrics, NetworkMetrics, DiskMetrics, ServerEvent
    from core.server_discovery import ServerDiscovery
    DATABASE_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Database modules not available: {e}")
    DATABASE_AVAILABLE = False

class MultiServerCollector:
    """Collects data from multiple remote servers"""
    
    def __init__(self):
        self.db_ops = None
        self.server_discovery = None
        self.collection_threads = {}
        self.collection_interval = 30  # seconds (less frequent than local to avoid SSH overhead)
        self.running = False
        self.executor = None
        
        # Initialize database operations
        if DATABASE_AVAILABLE:
            try:
                self.db_ops = DatabaseOperations()
                print("✅ Multi-server database operations initialized")
            except Exception as e:
                print(f"❌ Failed to initialize database operations: {e}")
                self.db_ops = None
        
        # Initialize server discovery
        try:
            self.server_discovery = ServerDiscovery()
            print("✅ Multi-server discovery initialized")
        except Exception as e:
            print(f"❌ Failed to initialize server discovery: {e}")
            self.server_discovery = None
        
        # Thread pool for parallel data collection
        self.executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="multi_server_collector")
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def start_collection(self):
        """Start collecting data from all connected servers"""
        if not self.db_ops or not self.server_discovery:
            print("❌ Cannot start collection: Database or server discovery not available")
            return False
        
        self.running = True
        
        # Start main collection loop in background thread
        collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        collection_thread.start()
        
        print(f"🚀 Multi-server data collection started (interval: {self.collection_interval}s)")
        return True
    
    def stop_collection(self):
        """Stop collecting data from all servers"""
        self.running = False
        if self.executor:
            self.executor.shutdown(wait=True)
        print("🛑 Multi-server data collection stopped")
    
    def _collection_loop(self):
        """Main collection loop"""
        while self.running:
            try:
                # Get list of connected servers
                connected_servers = self.server_discovery.get_connected_servers_info()
                
                if connected_servers:
                    print(f"🔄 Collecting data from {len(connected_servers)} servers...")
                    
                    # Collect data from all servers in parallel
                    futures = []
                    for server_ip, server_info in connected_servers.items():
                        if server_info.get('ssh_connected'):
                            future = self.executor.submit(self._collect_server_data, server_ip, server_info)
                            futures.append(future)
                    
                    # Wait for all collections to complete
                    completed = 0
                    for future in as_completed(futures, timeout=self.collection_interval - 5):
                        try:
                            result = future.result()
                            if result:
                                completed += 1
                        except Exception as e:
                            self.logger.error(f"Collection error: {e}")
                    
                    print(f"✅ Completed data collection from {completed}/{len(futures)} servers")
                else:
                    print("⚠️  No connected servers found for data collection")
                
                # Wait for next collection cycle
                time.sleep(self.collection_interval)
                
            except Exception as e:
                self.logger.error(f"Collection loop error: {e}")
                time.sleep(5)  # Short delay before retrying
    
    def _collect_server_data(self, server_ip: str, server_info: dict) -> bool:
        """Collect data from a single server"""
        try:
            print(f"📊 Collecting data from {server_ip} ({server_info.get('name', 'Unknown')})")
            
            # Get server connection details
            username = server_info.get('username', 'root')
            
            # Collect system metrics
            system_metrics = self._collect_system_metrics(server_ip, username)
            if system_metrics:
                self.db_ops.store_system_metrics(system_metrics)
            
            # Collect process metrics
            process_metrics = self._collect_process_metrics(server_ip, username)
            if process_metrics:
                self.db_ops.store_process_metrics(process_metrics)
            
            # Collect network metrics
            network_metrics = self._collect_network_metrics(server_ip, username)
            if network_metrics:
                self.db_ops.store_network_metrics(network_metrics)
            
            # Collect disk metrics
            disk_metrics = self._collect_disk_metrics(server_ip, username)
            if disk_metrics:
                self.db_ops.store_disk_metrics(disk_metrics)
            
            # Update server last seen
            self.db_ops.update_server_status(server_ip, "active")
            
            print(f"✅ Data collection completed for {server_ip}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error collecting data from {server_ip}: {e}")
            
            # Log server error event
            try:
                event = ServerEvent(
                    server_ip=server_ip,
                    event_type="error",
                    message=f"Data collection failed: {str(e)}",
                    severity="high"
                )
                self.db_ops.log_server_event(event)
            except:
                pass
            
            return False
    
    def _collect_system_metrics(self, server_ip: str, username: str) -> Optional[SystemMetrics]:
        """Collect system metrics from remote server"""
        try:
            # Get hostname
            hostname_cmd = "hostname"
            hostname_result = self._execute_ssh_command(server_ip, username, hostname_cmd)
            hostname = hostname_result.strip() if hostname_result else server_ip
            
            # Get CPU usage
            cpu_cmd = "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1"
            cpu_result = self._execute_ssh_command(server_ip, username, cpu_cmd)
            cpu_percent = float(cpu_result.strip()) if cpu_result and cpu_result.strip() else None
            
            # Get memory usage
            memory_cmd = "free | grep Mem | awk '{printf \"%.2f %.0f %.0f %.0f\", $3/$2 * 100.0, $3, $2, $7}'"
            memory_result = self._execute_ssh_command(server_ip, username, memory_cmd)
            memory_percent = memory_used = memory_total = memory_available = None
            
            if memory_result:
                memory_parts = memory_result.strip().split()
                if len(memory_parts) >= 4:
                    memory_percent = float(memory_parts[0])
                    memory_used = int(memory_parts[1]) * 1024  # Convert to bytes
                    memory_total = int(memory_parts[2]) * 1024  # Convert to bytes
                    memory_available = int(memory_parts[3]) * 1024  # Convert to bytes
            
            # Get swap usage
            swap_cmd = "free | grep Swap | awk '{if($2>0) printf \"%.2f\", $3/$2 * 100.0; else print \"0\"}'"
            swap_result = self._execute_ssh_command(server_ip, username, swap_cmd)
            swap_percent = float(swap_result.strip()) if swap_result and swap_result.strip() else 0.0
            
            # Get disk usage for root partition
            disk_cmd = "df / | tail -1 | awk '{print $5}' | cut -d'%' -f1"
            disk_result = self._execute_ssh_command(server_ip, username, disk_cmd)
            disk_percent = float(disk_result.strip()) if disk_result and disk_result.strip() else None
            
            # Get load average
            load_cmd = "uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | cut -d',' -f1"
            load_result = self._execute_ssh_command(server_ip, username, load_cmd)
            load_avg_1 = float(load_result.strip()) if load_result and load_result.strip() else None
            
            # Get uptime
            uptime_cmd = "cat /proc/uptime | awk '{print $1}'"
            uptime_result = self._execute_ssh_command(server_ip, username, uptime_cmd)
            uptime_seconds = int(float(uptime_result.strip())) if uptime_result and uptime_result.strip() else None
            
            # Create SystemMetrics object
            metrics = SystemMetrics(
                server_ip=server_ip,
                server_hostname=hostname,
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used=memory_used,
                memory_total=memory_total,
                memory_available=memory_available,
                swap_percent=swap_percent,
                disk_percent=disk_percent,
                load_avg_1=load_avg_1,
                uptime_seconds=uptime_seconds,
                is_remote=True  # Mark as remote server data
            )
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error collecting system metrics from {server_ip}: {e}")
            return None
    
    def _collect_process_metrics(self, server_ip: str, username: str) -> Optional[List[ProcessMetrics]]:
        """Collect process metrics from remote server"""
        try:
            # Get top 10 processes by CPU usage
            process_cmd = "ps aux --sort=-%cpu | head -11 | tail -10 | awk '{print $2\"|\"$11\"|\"$3\"|\"$4\"|\"$8}'"
            process_result = self._execute_ssh_command(server_ip, username, process_cmd)
            
            if not process_result:
                return None
            
            process_metrics = []
            for line in process_result.strip().split('\n'):
                if line.strip():
                    parts = line.split('|')
                    if len(parts) >= 5:
                        try:
                            pid = int(parts[0])
                            process_name = parts[1].split('/')[-1]  # Get just the process name
                            cpu_percent = float(parts[2])
                            memory_percent = float(parts[3])
                            status = parts[4]
                            
                            process_metric = ProcessMetrics(
                                server_ip=server_ip,
                                process_name=process_name,
                                pid=pid,
                                cpu_percent=cpu_percent,
                                memory_percent=memory_percent,
                                status=status,
                                command_line=parts[1]  # Full command line
                            )
                            process_metrics.append(process_metric)
                        except (ValueError, IndexError):
                            continue
            
            return process_metrics if process_metrics else None
            
        except Exception as e:
            self.logger.error(f"Error collecting process metrics from {server_ip}: {e}")
            return None
    
    def _collect_network_metrics(self, server_ip: str, username: str) -> Optional[NetworkMetrics]:
        """Collect network metrics from remote server"""
        try:
            # Get network statistics
            network_cmd = "cat /proc/net/dev | grep -E '(eth|ens|enp|wlan)' | head -1 | awk '{print $2\"|\"$10\"|\"$3\"|\"$11}'"
            network_result = self._execute_ssh_command(server_ip, username, network_cmd)
            
            if not network_result:
                return None
            
            parts = network_result.strip().split('|')
            if len(parts) >= 4:
                bytes_recv = int(parts[0])
                bytes_sent = int(parts[1])
                packets_recv = int(parts[2])
                packets_sent = int(parts[3])
                
                # Get interface count
                interface_cmd = "ls /sys/class/net | grep -v lo | wc -l"
                interface_result = self._execute_ssh_command(server_ip, username, interface_cmd)
                interface_count = int(interface_result.strip()) if interface_result else 1
                
                network_metrics = NetworkMetrics(
                    server_ip=server_ip,
                    bytes_sent=bytes_sent,
                    bytes_recv=bytes_recv,
                    packets_sent=packets_sent,
                    packets_recv=packets_recv,
                    interface_count=interface_count
                )
                
                return network_metrics
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error collecting network metrics from {server_ip}: {e}")
            return None
    
    def _collect_disk_metrics(self, server_ip: str, username: str) -> Optional[DiskMetrics]:
        """Collect disk metrics from remote server"""
        try:
            # Get disk usage for root partition
            disk_cmd = "df / | tail -1 | awk '{print $2\"|\"$3\"|\"$4\"|\"$5}'"
            disk_result = self._execute_ssh_command(server_ip, username, disk_cmd)
            
            if not disk_result:
                return None
            
            parts = disk_result.strip().split('|')
            if len(parts) >= 4:
                total_disk_space = int(parts[0]) * 1024  # Convert to bytes
                used_disk_space = int(parts[1]) * 1024   # Convert to bytes
                free_disk_space = int(parts[2]) * 1024   # Convert to bytes
                disk_percent = float(parts[3].rstrip('%'))
                
                disk_metrics = DiskMetrics(
                    server_ip=server_ip,
                    total_disk_space=total_disk_space,
                    used_disk_space=used_disk_space,
                    free_disk_space=free_disk_space,
                    disk_percent=disk_percent
                )
                
                return disk_metrics
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error collecting disk metrics from {server_ip}: {e}")
            return None
    
    def _execute_ssh_command(self, server_ip: str, username: str, command: str, timeout: int = 10) -> Optional[str]:
        """Execute SSH command on remote server"""
        try:
            # Handle Docker containers differently
            if server_ip.startswith('docker://'):
                container_id = server_ip.replace('docker://', '')
                result = subprocess.run([
                    'docker', 'exec', container_id, 'sh', '-c', command
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                universal_newlines=True, timeout=timeout)
            else:
                # Regular SSH command
                result = subprocess.run([
                    'ssh', '-o', 'ConnectTimeout=5',
                    '-o', 'StrictHostKeyChecking=no',
                    '-o', 'BatchMode=yes',
                    f'{username}@{server_ip}', command
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                universal_newlines=True, timeout=timeout)
            
            if result.returncode == 0:
                return result.stdout
            else:
                self.logger.warning(f"SSH command failed on {server_ip}: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            self.logger.warning(f"SSH command timeout on {server_ip}")
            return None
        except Exception as e:
            self.logger.error(f"SSH command error on {server_ip}: {e}")
            return None
    
    def get_collection_status(self) -> dict:
        """Get current collection status"""
        if not self.server_discovery:
            return {'status': 'disabled', 'servers': 0}
        
        connected_servers = self.server_discovery.get_connected_servers_info()
        
        return {
            'status': 'running' if self.running else 'stopped',
            'servers': len(connected_servers),
            'interval': self.collection_interval,
            'connected_servers': list(connected_servers.keys())
        }
    
    def register_discovered_servers(self):
        """Register all discovered servers in database"""
        if not self.db_ops or not self.server_discovery:
            return
        
        connected_servers = self.server_discovery.get_connected_servers_info()
        
        for server_ip, server_info in connected_servers.items():
            try:
                # Create server object
                server = Server(
                    server_ip=server_ip,
                    hostname=server_info.get('name', server_ip),
                    server_name=f"Remote Server ({server_info.get('name', server_ip)})",
                    os_info=server_info.get('os', 'Unknown'),
                    environment="production",
                    server_group="remote-servers"
                )
                
                # Register server
                success = self.db_ops.register_server(server)
                if success:
                    print(f"✅ Registered remote server: {server_ip} ({server_info.get('name')})")
                    
                    # Log connection event
                    event = ServerEvent(
                        server_ip=server_ip,
                        event_type="connected",
                        message=f"Remote server connected via SSH",
                        severity="info"
                    )
                    self.db_ops.log_server_event(event)
                
            except Exception as e:
                self.logger.error(f"Error registering server {server_ip}: {e}")

# Global instance for use by web dashboard
multi_server_collector = None

def get_multi_server_collector():
    """Get global multi-server collector instance"""
    global multi_server_collector
    if multi_server_collector is None:
        multi_server_collector = MultiServerCollector()
    return multi_server_collector