#!/usr/bin/env python3.12
"""
Historical Analytics Engine - Phase 4
Advanced analytics for historical data analysis and insights
"""

import os
import sys
import json
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from database.db_models import DatabaseOperations
    DATABASE_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Database modules not available: {e}")
    DATABASE_AVAILABLE = False

@dataclass
class AnalyticsResult:
    """Analytics result data structure"""
    metric_name: str
    server_ip: str
    server_hostname: str
    time_period: str
    analysis_type: str
    result_data: Dict[str, Any]
    insights: List[str]
    recommendations: List[str]
    generated_at: datetime

@dataclass
class PerformanceTrend:
    """Performance trend analysis"""
    metric: str
    trend_direction: str  # "increasing", "decreasing", "stable"
    trend_strength: float  # 0-100
    slope: float
    correlation: float
    prediction_7d: float
    confidence: float

@dataclass
class ResourceUtilizationSummary:
    """Resource utilization summary"""
    server_ip: str
    server_hostname: str
    time_period: str
    cpu_stats: Dict[str, float]
    memory_stats: Dict[str, float]
    disk_stats: Dict[str, float]
    load_stats: Dict[str, float]
    efficiency_score: float
    bottlenecks: List[str]

class HistoricalAnalytics:
    """Historical analytics engine"""
    
    def __init__(self):
        self.db_ops = None
        
        # Initialize database operations
        if DATABASE_AVAILABLE:
            try:
                self.db_ops = DatabaseOperations()
                print("✅ Historical analytics database operations initialized")
            except Exception as e:
                print(f"❌ Failed to initialize analytics database operations: {e}")
                self.db_ops = None
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def analyze_server_performance(self, server_ip: str, hours: int = 24) -> Optional[ResourceUtilizationSummary]:
        """Analyze server performance over time period"""
        try:
            if not self.db_ops:
                return None
            
            # Get server info
            server = self.db_ops.get_server(server_ip)
            if not server:
                return None
            
            # Get historical metrics
            metrics = self.db_ops.get_system_metrics_history(server_ip, hours)
            
            if not metrics or len(metrics) < 10:
                return None
            
            # Extract metric values (convert to float to handle Decimal types)
            cpu_values = [float(m.cpu_percent) for m in metrics if m.cpu_percent is not None]
            memory_values = [float(m.memory_percent) for m in metrics if m.memory_percent is not None]
            disk_values = [float(m.disk_percent) for m in metrics if m.disk_percent is not None]
            load_values = [float(m.load_avg_1) for m in metrics if m.load_avg_1 is not None]
            
            # Calculate statistics
            cpu_stats = self._calculate_metric_stats(cpu_values, "CPU")
            memory_stats = self._calculate_metric_stats(memory_values, "Memory")
            disk_stats = self._calculate_metric_stats(disk_values, "Disk")
            load_stats = self._calculate_metric_stats(load_values, "Load")
            
            # Calculate efficiency score
            efficiency_score = self._calculate_efficiency_score(cpu_stats, memory_stats, disk_stats)
            
            # Identify bottlenecks
            bottlenecks = self._identify_bottlenecks(cpu_stats, memory_stats, disk_stats, load_stats)
            
            return ResourceUtilizationSummary(
                server_ip=server_ip,
                server_hostname=server.hostname,
                time_period=f"{hours} hours",
                cpu_stats=cpu_stats,
                memory_stats=memory_stats,
                disk_stats=disk_stats,
                load_stats=load_stats,
                efficiency_score=efficiency_score,
                bottlenecks=bottlenecks
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing server performance: {e}")
            return None
    
    def analyze_performance_trends(self, server_ip: str, days: int = 7) -> List[PerformanceTrend]:
        """Analyze performance trends over multiple days"""
        try:
            if not self.db_ops:
                return []
            
            # Get extended historical data
            metrics = self.db_ops.get_system_metrics_history(server_ip, days * 24)
            
            if not metrics or len(metrics) < 50:
                return []
            
            trends = []
            
            # Analyze CPU trend
            cpu_values = [float(m.cpu_percent) for m in metrics if m.cpu_percent is not None]
            if cpu_values:
                cpu_trend = self._analyze_metric_trend(cpu_values, "cpu_percent")
                trends.append(cpu_trend)
            
            # Analyze Memory trend
            memory_values = [float(m.memory_percent) for m in metrics if m.memory_percent is not None]
            if memory_values:
                memory_trend = self._analyze_metric_trend(memory_values, "memory_percent")
                trends.append(memory_trend)
            
            # Analyze Disk trend
            disk_values = [float(m.disk_percent) for m in metrics if m.disk_percent is not None]
            if disk_values:
                disk_trend = self._analyze_metric_trend(disk_values, "disk_percent")
                trends.append(disk_trend)
            
            return trends
            
        except Exception as e:
            self.logger.error(f"Error analyzing performance trends: {e}")
            return []
    
    def generate_capacity_forecast(self, server_ip: str, days_ahead: int = 30) -> Dict[str, Any]:
        """Generate capacity forecast based on historical trends"""
        try:
            if not self.db_ops:
                return {}
            
            # Get historical data for trend analysis
            metrics = self.db_ops.get_system_metrics_history(server_ip, 7 * 24)  # 7 days
            
            if not metrics or len(metrics) < 100:
                return {'error': 'Insufficient historical data for forecasting'}
            
            # Analyze trends for each metric
            cpu_values = [m.cpu_percent for m in metrics if m.cpu_percent is not None]
            memory_values = [m.memory_percent for m in metrics if m.memory_percent is not None]
            disk_values = [m.disk_percent for m in metrics if m.disk_percent is not None]
            
            forecast = {}
            
            # CPU forecast
            if cpu_values:
                cpu_forecast = self._forecast_metric(cpu_values, days_ahead, "CPU")
                forecast['cpu'] = cpu_forecast
            
            # Memory forecast
            if memory_values:
                memory_forecast = self._forecast_metric(memory_values, days_ahead, "Memory")
                forecast['memory'] = memory_forecast
            
            # Disk forecast
            if disk_values:
                disk_forecast = self._forecast_metric(disk_values, days_ahead, "Disk")
                forecast['disk'] = disk_forecast
            
            # Generate recommendations
            recommendations = self._generate_capacity_recommendations(forecast)
            forecast['recommendations'] = recommendations
            
            return forecast
            
        except Exception as e:
            self.logger.error(f"Error generating capacity forecast: {e}")
            return {'error': str(e)}
    
    def analyze_peak_usage_patterns(self, server_ip: str, days: int = 7) -> Dict[str, Any]:
        """Analyze peak usage patterns and identify recurring patterns"""
        try:
            if not self.db_ops:
                return {}
            
            metrics = self.db_ops.get_system_metrics_history(server_ip, days * 24)
            
            if not metrics:
                return {}
            
            # Group metrics by hour of day
            hourly_patterns = {}
            daily_patterns = {}
            
            for metric in metrics:
                if not metric.timestamp:
                    continue
                
                hour = metric.timestamp.hour
                day = metric.timestamp.strftime('%A')  # Day name
                
                if hour not in hourly_patterns:
                    hourly_patterns[hour] = {'cpu': [], 'memory': [], 'disk': []}
                if day not in daily_patterns:
                    daily_patterns[day] = {'cpu': [], 'memory': [], 'disk': []}
                
                if metric.cpu_percent is not None:
                    hourly_patterns[hour]['cpu'].append(metric.cpu_percent)
                    daily_patterns[day]['cpu'].append(metric.cpu_percent)
                
                if metric.memory_percent is not None:
                    hourly_patterns[hour]['memory'].append(metric.memory_percent)
                    daily_patterns[day]['memory'].append(metric.memory_percent)
                
                if metric.disk_percent is not None:
                    hourly_patterns[hour]['disk'].append(metric.disk_percent)
                    daily_patterns[day]['disk'].append(metric.disk_percent)
            
            # Calculate average usage by hour
            hourly_averages = {}
            for hour, data in hourly_patterns.items():
                hourly_averages[hour] = {
                    'cpu_avg': statistics.mean(data['cpu']) if data['cpu'] else 0,
                    'memory_avg': statistics.mean(data['memory']) if data['memory'] else 0,
                    'disk_avg': statistics.mean(data['disk']) if data['disk'] else 0
                }
            
            # Calculate average usage by day
            daily_averages = {}
            for day, data in daily_patterns.items():
                daily_averages[day] = {
                    'cpu_avg': statistics.mean(data['cpu']) if data['cpu'] else 0,
                    'memory_avg': statistics.mean(data['memory']) if data['memory'] else 0,
                    'disk_avg': statistics.mean(data['disk']) if data['disk'] else 0
                }
            
            # Identify peak hours and days
            peak_analysis = self._identify_peak_patterns(hourly_averages, daily_averages)
            
            return {
                'hourly_patterns': hourly_averages,
                'daily_patterns': daily_averages,
                'peak_analysis': peak_analysis,
                'analysis_period': f"{days} days",
                'total_data_points': len(metrics)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing peak usage patterns: {e}")
            return {'error': str(e)}
    
    def compare_servers_performance(self, server_ips: List[str], hours: int = 24) -> Dict[str, Any]:
        """Compare performance across multiple servers"""
        try:
            if not self.db_ops:
                return {}
            
            server_comparisons = {}
            
            for server_ip in server_ips:
                analysis = self.analyze_server_performance(server_ip, hours)
                if analysis:
                    server_comparisons[server_ip] = {
                        'hostname': analysis.server_hostname,
                        'cpu_avg': analysis.cpu_stats.get('average', 0),
                        'memory_avg': analysis.memory_stats.get('average', 0),
                        'disk_avg': analysis.disk_stats.get('average', 0),
                        'efficiency_score': analysis.efficiency_score,
                        'bottlenecks': analysis.bottlenecks
                    }
            
            if not server_comparisons:
                return {}
            
            # Calculate rankings
            rankings = self._calculate_server_rankings(server_comparisons)
            
            # Generate comparison insights
            insights = self._generate_comparison_insights(server_comparisons, rankings)
            
            return {
                'servers': server_comparisons,
                'rankings': rankings,
                'insights': insights,
                'comparison_period': f"{hours} hours"
            }
            
        except Exception as e:
            self.logger.error(f"Error comparing servers performance: {e}")
            return {'error': str(e)}
    
    def _calculate_metric_stats(self, values: List[float], metric_name: str) -> Dict[str, float]:
        """Calculate comprehensive statistics for a metric"""
        if not values:
            return {}
        
        sorted_values = sorted(values)
        n = len(values)
        
        stats = {
            'count': n,
            'average': statistics.mean(values),
            'median': statistics.median(values),
            'min': min(values),
            'max': max(values),
            'std_dev': statistics.stdev(values) if n > 1 else 0,
            'percentile_95': sorted_values[int(0.95 * n)] if n > 0 else 0,
            'percentile_99': sorted_values[int(0.99 * n)] if n > 0 else 0
        }
        
        # Calculate additional metrics
        stats['range'] = stats['max'] - stats['min']
        stats['coefficient_of_variation'] = (stats['std_dev'] / stats['average']) * 100 if stats['average'] > 0 else 0
        
        return stats
    
    def _analyze_metric_trend(self, values: List[float], metric_name: str) -> PerformanceTrend:
        """Analyze trend for a specific metric"""
        if len(values) < 10:
            return PerformanceTrend(
                metric=metric_name,
                trend_direction="insufficient_data",
                trend_strength=0,
                slope=0,
                correlation=0,
                prediction_7d=0,
                confidence=0
            )
        
        # Calculate trend slope using linear regression
        n = len(values)
        x_values = list(range(n))
        
        x_mean = sum(x_values) / n
        y_mean = sum(values) / n
        
        numerator = sum((x_values[i] - x_mean) * (values[i] - y_mean) for i in range(n))
        denominator = sum((x_values[i] - x_mean) ** 2 for i in range(n))
        
        slope = numerator / denominator if denominator != 0 else 0
        
        # Calculate correlation coefficient
        y_variance = sum((values[i] - y_mean) ** 2 for i in range(n))
        correlation = abs(numerator) / (denominator * y_variance) ** 0.5 if denominator > 0 and y_variance > 0 else 0
        
        # Determine trend direction and strength
        if abs(slope) < 0.01:
            trend_direction = "stable"
            trend_strength = 0
        elif slope > 0:
            trend_direction = "increasing"
            trend_strength = min(100, abs(slope) * 100)
        else:
            trend_direction = "decreasing"
            trend_strength = min(100, abs(slope) * 100)
        
        # Simple prediction for 7 days ahead (assuming same trend continues)
        prediction_7d = values[-1] + (slope * 7 * 24)  # 7 days * 24 hours
        prediction_7d = max(0, min(100, prediction_7d))  # Clamp to 0-100%
        
        # Confidence based on correlation and data consistency
        confidence = correlation * 100
        
        return PerformanceTrend(
            metric=metric_name,
            trend_direction=trend_direction,
            trend_strength=trend_strength,
            slope=slope,
            correlation=correlation,
            prediction_7d=prediction_7d,
            confidence=confidence
        )
    
    def _forecast_metric(self, values: List[float], days_ahead: int, metric_name: str) -> Dict[str, Any]:
        """Forecast metric values for future period"""
        if len(values) < 20:
            return {'error': 'Insufficient data for forecasting'}
        
        # Simple linear trend forecasting
        trend = self._analyze_metric_trend(values, metric_name)
        
        current_value = values[-1]
        forecasted_value = current_value + (trend.slope * days_ahead * 24)
        forecasted_value = max(0, min(100, forecasted_value))
        
        # Calculate confidence intervals
        std_dev = statistics.stdev(values)
        confidence_interval = std_dev * 1.96  # 95% confidence interval
        
        forecast = {
            'current_value': current_value,
            'forecasted_value': forecasted_value,
            'change_expected': forecasted_value - current_value,
            'trend_direction': trend.trend_direction,
            'confidence': trend.confidence,
            'confidence_interval': {
                'lower': max(0, forecasted_value - confidence_interval),
                'upper': min(100, forecasted_value + confidence_interval)
            },
            'days_ahead': days_ahead
        }
        
        # Add risk assessment
        if forecasted_value > 90:
            forecast['risk_level'] = 'HIGH'
            forecast['risk_message'] = f'{metric_name} expected to reach critical levels'
        elif forecasted_value > 80:
            forecast['risk_level'] = 'MEDIUM'
            forecast['risk_message'] = f'{metric_name} approaching high utilization'
        else:
            forecast['risk_level'] = 'LOW'
            forecast['risk_message'] = f'{metric_name} within acceptable range'
        
        return forecast
    
    def _calculate_efficiency_score(self, cpu_stats: Dict, memory_stats: Dict, disk_stats: Dict) -> float:
        """Calculate overall efficiency score (0-100)"""
        if not cpu_stats or not memory_stats or not disk_stats:
            return 0
        
        # Efficiency is inversely related to resource utilization
        # Lower utilization with consistent performance = higher efficiency
        
        cpu_efficiency = max(0, 100 - cpu_stats.get('average', 0))
        memory_efficiency = max(0, 100 - memory_stats.get('average', 0))
        disk_efficiency = max(0, 100 - disk_stats.get('average', 0))
        
        # Weight CPU and memory more heavily than disk
        weighted_efficiency = (cpu_efficiency * 0.4 + memory_efficiency * 0.4 + disk_efficiency * 0.2)
        
        # Penalize high variability (inconsistent performance)
        cpu_variability_penalty = cpu_stats.get('coefficient_of_variation', 0) * 0.1
        memory_variability_penalty = memory_stats.get('coefficient_of_variation', 0) * 0.1
        
        final_score = max(0, weighted_efficiency - cpu_variability_penalty - memory_variability_penalty)
        
        return round(final_score, 1)
    
    def _identify_bottlenecks(self, cpu_stats: Dict, memory_stats: Dict, 
                            disk_stats: Dict, load_stats: Dict) -> List[str]:
        """Identify performance bottlenecks"""
        bottlenecks = []
        
        # CPU bottleneck
        if cpu_stats.get('average', 0) > 80:
            bottlenecks.append(f"High CPU utilization (avg: {cpu_stats.get('average', 0):.1f}%)")
        elif cpu_stats.get('percentile_95', 0) > 90:
            bottlenecks.append(f"CPU spikes detected (95th percentile: {cpu_stats.get('percentile_95', 0):.1f}%)")
        
        # Memory bottleneck
        if memory_stats.get('average', 0) > 85:
            bottlenecks.append(f"High memory utilization (avg: {memory_stats.get('average', 0):.1f}%)")
        elif memory_stats.get('percentile_95', 0) > 95:
            bottlenecks.append(f"Memory pressure detected (95th percentile: {memory_stats.get('percentile_95', 0):.1f}%)")
        
        # Disk bottleneck
        if disk_stats.get('average', 0) > 90:
            bottlenecks.append(f"High disk utilization (avg: {disk_stats.get('average', 0):.1f}%)")
        
        # Load bottleneck
        if load_stats.get('average', 0) > 4:
            bottlenecks.append(f"High system load (avg: {load_stats.get('average', 0):.1f})")
        
        # Variability issues
        if cpu_stats.get('coefficient_of_variation', 0) > 50:
            bottlenecks.append("Inconsistent CPU performance")
        
        if memory_stats.get('coefficient_of_variation', 0) > 30:
            bottlenecks.append("Inconsistent memory usage patterns")
        
        return bottlenecks
    
    def _identify_peak_patterns(self, hourly_averages: Dict, daily_averages: Dict) -> Dict[str, Any]:
        """Identify peak usage patterns"""
        # Find peak hours
        peak_cpu_hour = max(hourly_averages.keys(), key=lambda h: hourly_averages[h]['cpu_avg'])
        peak_memory_hour = max(hourly_averages.keys(), key=lambda h: hourly_averages[h]['memory_avg'])
        
        # Find peak days
        peak_cpu_day = max(daily_averages.keys(), key=lambda d: daily_averages[d]['cpu_avg'])
        peak_memory_day = max(daily_averages.keys(), key=lambda d: daily_averages[d]['memory_avg'])
        
        return {
            'peak_hours': {
                'cpu': {
                    'hour': peak_cpu_hour,
                    'value': hourly_averages[peak_cpu_hour]['cpu_avg']
                },
                'memory': {
                    'hour': peak_memory_hour,
                    'value': hourly_averages[peak_memory_hour]['memory_avg']
                }
            },
            'peak_days': {
                'cpu': {
                    'day': peak_cpu_day,
                    'value': daily_averages[peak_cpu_day]['cpu_avg']
                },
                'memory': {
                    'day': peak_memory_day,
                    'value': daily_averages[peak_memory_day]['memory_avg']
                }
            }
        }
    
    def _calculate_server_rankings(self, server_comparisons: Dict) -> Dict[str, Any]:
        """Calculate server rankings based on performance metrics"""
        servers = list(server_comparisons.keys())
        
        # Rank by efficiency score (higher is better)
        efficiency_ranking = sorted(servers, 
                                  key=lambda s: server_comparisons[s]['efficiency_score'], 
                                  reverse=True)
        
        # Rank by CPU usage (lower is better)
        cpu_ranking = sorted(servers, 
                           key=lambda s: server_comparisons[s]['cpu_avg'])
        
        # Rank by memory usage (lower is better)
        memory_ranking = sorted(servers, 
                              key=lambda s: server_comparisons[s]['memory_avg'])
        
        return {
            'efficiency': efficiency_ranking,
            'cpu_performance': cpu_ranking,
            'memory_performance': memory_ranking
        }
    
    def _generate_comparison_insights(self, server_comparisons: Dict, rankings: Dict) -> List[str]:
        """Generate insights from server comparison"""
        insights = []
        
        if not server_comparisons:
            return insights
        
        # Best performing server
        best_server = rankings['efficiency'][0]
        best_hostname = server_comparisons[best_server]['hostname']
        best_score = server_comparisons[best_server]['efficiency_score']
        
        insights.append(f"Best performing server: {best_hostname} ({best_server}) with efficiency score {best_score:.1f}")
        
        # Worst performing server
        worst_server = rankings['efficiency'][-1]
        worst_hostname = server_comparisons[worst_server]['hostname']
        worst_score = server_comparisons[worst_server]['efficiency_score']
        
        insights.append(f"Server needing attention: {worst_hostname} ({worst_server}) with efficiency score {worst_score:.1f}")
        
        # Resource utilization insights
        avg_cpu = statistics.mean([data['cpu_avg'] for data in server_comparisons.values()])
        avg_memory = statistics.mean([data['memory_avg'] for data in server_comparisons.values()])
        
        insights.append(f"Average CPU utilization across servers: {avg_cpu:.1f}%")
        insights.append(f"Average memory utilization across servers: {avg_memory:.1f}%")
        
        # Bottleneck analysis
        servers_with_bottlenecks = sum(1 for data in server_comparisons.values() if data['bottlenecks'])
        if servers_with_bottlenecks > 0:
            insights.append(f"{servers_with_bottlenecks} out of {len(server_comparisons)} servers have performance bottlenecks")
        
        return insights
    
    def _generate_capacity_recommendations(self, forecast: Dict) -> List[str]:
        """Generate capacity planning recommendations"""
        recommendations = []
        
        for metric, data in forecast.items():
            if metric == 'recommendations':
                continue
                
            if isinstance(data, dict) and 'risk_level' in data:
                if data['risk_level'] == 'HIGH':
                    recommendations.append(f"URGENT: {metric.upper()} capacity upgrade needed - expected to reach {data['forecasted_value']:.1f}%")
                elif data['risk_level'] == 'MEDIUM':
                    recommendations.append(f"PLAN: Consider {metric.upper()} capacity expansion - trending toward {data['forecasted_value']:.1f}%")
                elif data['change_expected'] > 10:
                    recommendations.append(f"MONITOR: {metric.upper()} usage increasing - watch for capacity needs")
        
        if not recommendations:
            recommendations.append("Current capacity appears sufficient for forecasted period")
        
        return recommendations

# Global instance
historical_analytics = None

def get_historical_analytics():
    """Get global historical analytics instance"""
    global historical_analytics
    if historical_analytics is None:
        historical_analytics = HistoricalAnalytics()
    return historical_analytics