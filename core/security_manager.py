#!/usr/bin/env python3.12
"""
Security & Authentication Manager - Phase 5
Enterprise-grade security and authentication system
"""

import os
import sys
import json
import hashlib
import secrets
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
import logging
import jwt
from functools import wraps

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from config.production_config import get_production_config
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False

@dataclass
class User:
    """User model"""
    username: str
    email: str
    password_hash: str
    role: str = "user"  # user, admin, superuser
    active: bool = True
    created_at: datetime = None
    last_login: datetime = None
    failed_attempts: int = 0
    locked_until: datetime = None
    api_key: str = None

@dataclass
class Session:
    """Session model"""
    session_id: str
    username: str
    created_at: datetime
    expires_at: datetime
    ip_address: str
    user_agent: str
    active: bool = True

@dataclass
class APIKey:
    """API Key model"""
    key_id: str
    key_hash: str
    username: str
    name: str
    permissions: List[str]
    created_at: datetime
    expires_at: datetime = None
    active: bool = True

class SecurityManager:
    """Security and authentication manager"""
    
    def __init__(self):
        self.config = get_production_config() if CONFIG_AVAILABLE else None
        self.users = {}  # In production, this would be a database
        self.sessions = {}
        self.api_keys = {}
        self.failed_attempts = {}  # Track failed login attempts by IP
        
        # Security settings
        self.jwt_secret = secrets.token_urlsafe(32)
        self.session_timeout = 3600  # 1 hour
        self.max_failed_attempts = 5
        self.lockout_duration = 900  # 15 minutes
        
        if self.config:
            self.session_timeout = self.config.security.session_timeout
            self.max_failed_attempts = self.config.security.max_login_attempts
            self.lockout_duration = self.config.security.lockout_duration
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Create default admin user
        self._create_default_users()
        
        print("✅ Security manager initialized")
    
    def _create_default_users(self):
        """Create default users for initial setup"""
        # Create admin user
        admin_password = "admin123!"  # In production, this should be changed immediately
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            password_hash=self._hash_password(admin_password),
            role="superuser",
            created_at=datetime.now()
        )
        self.users["admin"] = admin_user
        
        # Create default user
        user_password = "user123!"
        default_user = User(
            username="user",
            email="<EMAIL>",
            password_hash=self._hash_password(user_password),
            role="user",
            created_at=datetime.now()
        )
        self.users["user"] = default_user
        
        print("✅ Default users created (admin/admin123!, user/user123!)")
    
    def _hash_password(self, password: str) -> str:
        """Hash password using SHA-256 with salt"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        return f"{salt}:{password_hash}"
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash"""
        try:
            salt, hash_value = password_hash.split(':')
            return hashlib.sha256((password + salt).encode()).hexdigest() == hash_value
        except ValueError:
            return False
    
    def _generate_session_id(self) -> str:
        """Generate secure session ID"""
        return secrets.token_urlsafe(32)
    
    def _generate_api_key(self) -> Tuple[str, str]:
        """Generate API key and its hash"""
        key = f"pk_{secrets.token_urlsafe(32)}"
        key_hash = hashlib.sha256(key.encode()).hexdigest()
        return key, key_hash
    
    def _is_ip_locked(self, ip_address: str) -> bool:
        """Check if IP address is locked due to failed attempts"""
        if ip_address not in self.failed_attempts:
            return False
        
        attempts_data = self.failed_attempts[ip_address]
        if attempts_data['count'] >= self.max_failed_attempts:
            if datetime.now() < attempts_data['locked_until']:
                return True
            else:
                # Lockout expired, reset attempts
                del self.failed_attempts[ip_address]
        
        return False
    
    def _record_failed_attempt(self, ip_address: str):
        """Record failed login attempt"""
        if ip_address not in self.failed_attempts:
            self.failed_attempts[ip_address] = {'count': 0, 'locked_until': None}
        
        self.failed_attempts[ip_address]['count'] += 1
        
        if self.failed_attempts[ip_address]['count'] >= self.max_failed_attempts:
            self.failed_attempts[ip_address]['locked_until'] = datetime.now() + timedelta(seconds=self.lockout_duration)
            self.logger.warning(f"IP {ip_address} locked due to {self.max_failed_attempts} failed attempts")
    
    def _clear_failed_attempts(self, ip_address: str):
        """Clear failed attempts for IP after successful login"""
        if ip_address in self.failed_attempts:
            del self.failed_attempts[ip_address]
    
    def authenticate_user(self, username: str, password: str, ip_address: str = None, user_agent: str = None) -> Optional[Dict]:
        """Authenticate user credentials"""
        try:
            # Check IP lockout
            if ip_address and self._is_ip_locked(ip_address):
                return {
                    'success': False,
                    'error': 'IP address temporarily locked due to failed attempts',
                    'locked_until': self.failed_attempts[ip_address]['locked_until'].isoformat()
                }
            
            # Check if user exists
            if username not in self.users:
                if ip_address:
                    self._record_failed_attempt(ip_address)
                return {'success': False, 'error': 'Invalid credentials'}
            
            user = self.users[username]
            
            # Check if user is active
            if not user.active:
                return {'success': False, 'error': 'Account disabled'}
            
            # Check if user is locked
            if user.locked_until and datetime.now() < user.locked_until:
                return {
                    'success': False,
                    'error': 'Account temporarily locked',
                    'locked_until': user.locked_until.isoformat()
                }
            
            # Verify password
            if not self._verify_password(password, user.password_hash):
                user.failed_attempts += 1
                
                # Lock user account after max attempts
                if user.failed_attempts >= self.max_failed_attempts:
                    user.locked_until = datetime.now() + timedelta(seconds=self.lockout_duration)
                
                if ip_address:
                    self._record_failed_attempt(ip_address)
                
                return {'success': False, 'error': 'Invalid credentials'}
            
            # Successful authentication
            user.failed_attempts = 0
            user.locked_until = None
            user.last_login = datetime.now()
            
            if ip_address:
                self._clear_failed_attempts(ip_address)
            
            # Create session
            session = self.create_session(username, ip_address, user_agent)
            
            return {
                'success': True,
                'user': {
                    'username': user.username,
                    'email': user.email,
                    'role': user.role,
                    'last_login': user.last_login.isoformat()
                },
                'session': {
                    'session_id': session.session_id,
                    'expires_at': session.expires_at.isoformat()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return {'success': False, 'error': 'Authentication failed'}
    
    def create_session(self, username: str, ip_address: str = None, user_agent: str = None) -> Session:
        """Create new user session"""
        session_id = self._generate_session_id()
        expires_at = datetime.now() + timedelta(seconds=self.session_timeout)
        
        session = Session(
            session_id=session_id,
            username=username,
            created_at=datetime.now(),
            expires_at=expires_at,
            ip_address=ip_address or "unknown",
            user_agent=user_agent or "unknown"
        )
        
        self.sessions[session_id] = session
        return session
    
    def validate_session(self, session_id: str) -> Optional[Dict]:
        """Validate session and return user info"""
        try:
            if session_id not in self.sessions:
                return None
            
            session = self.sessions[session_id]
            
            # Check if session is active
            if not session.active:
                return None
            
            # Check if session has expired
            if datetime.now() > session.expires_at:
                session.active = False
                return None
            
            # Get user info
            if session.username not in self.users:
                return None
            
            user = self.users[session.username]
            
            # Check if user is still active
            if not user.active:
                return None
            
            return {
                'username': user.username,
                'email': user.email,
                'role': user.role,
                'session_id': session.session_id,
                'expires_at': session.expires_at.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Session validation error: {e}")
            return None
    
    def logout_session(self, session_id: str) -> bool:
        """Logout session"""
        try:
            if session_id in self.sessions:
                self.sessions[session_id].active = False
                return True
            return False
        except Exception as e:
            self.logger.error(f"Logout error: {e}")
            return False
    
    def create_api_key(self, username: str, name: str, permissions: List[str] = None, expires_days: int = None) -> Optional[Dict]:
        """Create API key for user"""
        try:
            if username not in self.users:
                return None
            
            key, key_hash = self._generate_api_key()
            key_id = secrets.token_hex(8)
            
            expires_at = None
            if expires_days:
                expires_at = datetime.now() + timedelta(days=expires_days)
            
            api_key = APIKey(
                key_id=key_id,
                key_hash=key_hash,
                username=username,
                name=name,
                permissions=permissions or ["read"],
                created_at=datetime.now(),
                expires_at=expires_at
            )
            
            self.api_keys[key_id] = api_key
            
            return {
                'key_id': key_id,
                'api_key': key,  # Only returned once
                'name': name,
                'permissions': permissions or ["read"],
                'expires_at': expires_at.isoformat() if expires_at else None
            }
            
        except Exception as e:
            self.logger.error(f"API key creation error: {e}")
            return None
    
    def validate_api_key(self, api_key: str) -> Optional[Dict]:
        """Validate API key and return user info"""
        try:
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            
            for key_id, api_key_obj in self.api_keys.items():
                if api_key_obj.key_hash == key_hash and api_key_obj.active:
                    # Check expiration
                    if api_key_obj.expires_at and datetime.now() > api_key_obj.expires_at:
                        api_key_obj.active = False
                        return None
                    
                    # Get user info
                    if api_key_obj.username not in self.users:
                        return None
                    
                    user = self.users[api_key_obj.username]
                    
                    return {
                        'username': user.username,
                        'email': user.email,
                        'role': user.role,
                        'key_id': key_id,
                        'permissions': api_key_obj.permissions
                    }
            
            return None
            
        except Exception as e:
            self.logger.error(f"API key validation error: {e}")
            return None
    
    def create_user(self, username: str, email: str, password: str, role: str = "user") -> bool:
        """Create new user"""
        try:
            if username in self.users:
                return False
            
            # Validate password strength
            if not self._validate_password_strength(password):
                return False
            
            user = User(
                username=username,
                email=email,
                password_hash=self._hash_password(password),
                role=role,
                created_at=datetime.now()
            )
            
            self.users[username] = user
            self.logger.info(f"User created: {username}")
            return True
            
        except Exception as e:
            self.logger.error(f"User creation error: {e}")
            return False
    
    def _validate_password_strength(self, password: str) -> bool:
        """Validate password strength"""
        if not self.config:
            return len(password) >= 8
        
        min_length = self.config.security.password_min_length
        require_special = self.config.security.password_require_special
        
        if len(password) < min_length:
            return False
        
        if require_special:
            special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
            if not any(c in special_chars for c in password):
                return False
        
        return True
    
    def change_password(self, username: str, old_password: str, new_password: str) -> bool:
        """Change user password"""
        try:
            if username not in self.users:
                return False
            
            user = self.users[username]
            
            # Verify old password
            if not self._verify_password(old_password, user.password_hash):
                return False
            
            # Validate new password
            if not self._validate_password_strength(new_password):
                return False
            
            # Update password
            user.password_hash = self._hash_password(new_password)
            self.logger.info(f"Password changed for user: {username}")
            return True
            
        except Exception as e:
            self.logger.error(f"Password change error: {e}")
            return False
    
    def get_security_status(self) -> Dict:
        """Get security system status"""
        active_sessions = sum(1 for s in self.sessions.values() if s.active and datetime.now() <= s.expires_at)
        active_api_keys = sum(1 for k in self.api_keys.values() if k.active)
        locked_ips = sum(1 for a in self.failed_attempts.values() if a['locked_until'] and datetime.now() < a['locked_until'])
        
        return {
            'total_users': len(self.users),
            'active_sessions': active_sessions,
            'active_api_keys': active_api_keys,
            'locked_ips': locked_ips,
            'authentication_enabled': self.config.security.authentication_enabled if self.config else True,
            'session_timeout': self.session_timeout,
            'max_failed_attempts': self.max_failed_attempts
        }

def require_auth(permission: str = None):
    """Decorator to require authentication for API endpoints"""
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # This would be implemented in the web framework
            # For now, it's a placeholder
            return func(self, *args, **kwargs)
        return wrapper
    return decorator

def require_role(role: str):
    """Decorator to require specific role"""
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # This would be implemented in the web framework
            # For now, it's a placeholder
            return func(self, *args, **kwargs)
        return wrapper
    return decorator

# Global security manager instance
security_manager = None

def get_security_manager():
    """Get global security manager instance"""
    global security_manager
    if security_manager is None:
        security_manager = SecurityManager()
    return security_manager