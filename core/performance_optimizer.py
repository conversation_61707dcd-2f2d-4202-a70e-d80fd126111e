#!/usr/bin/env python3.12
"""
Performance Optimization Engine - Phase 5
Advanced performance optimization and caching system
"""

import os
import sys
import time
import threading
import gzip
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import logging
from functools import wraps, lru_cache
import asyncio
from concurrent.futures import ThreadPoolExecutor

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from config.production_config import get_production_config
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    value: Any
    created_at: datetime
    expires_at: datetime
    access_count: int = 0
    last_accessed: datetime = None
    size_bytes: int = 0

@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    cache_hits: int = 0
    cache_misses: int = 0
    cache_size: int = 0
    compression_ratio: float = 0.0
    avg_response_time: float = 0.0
    total_requests: int = 0
    active_connections: int = 0
    memory_usage: float = 0.0

class InMemoryCache:
    """High-performance in-memory cache with TTL and LRU eviction"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache = {}
        self.access_order = []  # For LRU eviction
        self.lock = threading.RLock()
        self.metrics = PerformanceMetrics()
        
        # Start cleanup thread
        self.cleanup_thread = threading.Thread(target=self._cleanup_expired, daemon=True)
        self.cleanup_thread.start()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        with self.lock:
            if key not in self.cache:
                self.metrics.cache_misses += 1
                return None
            
            entry = self.cache[key]
            
            # Check expiration
            if datetime.now() > entry.expires_at:
                del self.cache[key]
                if key in self.access_order:
                    self.access_order.remove(key)
                self.metrics.cache_misses += 1
                return None
            
            # Update access info
            entry.access_count += 1
            entry.last_accessed = datetime.now()
            
            # Update LRU order
            if key in self.access_order:
                self.access_order.remove(key)
            self.access_order.append(key)
            
            self.metrics.cache_hits += 1
            return entry.value
    
    def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """Set value in cache"""
        with self.lock:
            ttl = ttl or self.default_ttl
            expires_at = datetime.now() + timedelta(seconds=ttl)
            
            # Calculate size
            size_bytes = sys.getsizeof(value)
            
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.now(),
                expires_at=expires_at,
                size_bytes=size_bytes
            )
            
            # Check if we need to evict
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            self.cache[key] = entry
            
            # Update access order
            if key in self.access_order:
                self.access_order.remove(key)
            self.access_order.append(key)
            
            self.metrics.cache_size = len(self.cache)
            return True
    
    def delete(self, key: str) -> bool:
        """Delete key from cache"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                if key in self.access_order:
                    self.access_order.remove(key)
                self.metrics.cache_size = len(self.cache)
                return True
            return False
    
    def clear(self):
        """Clear all cache entries"""
        with self.lock:
            self.cache.clear()
            self.access_order.clear()
            self.metrics.cache_size = 0
    
    def _evict_lru(self):
        """Evict least recently used entry"""
        if self.access_order:
            lru_key = self.access_order.pop(0)
            if lru_key in self.cache:
                del self.cache[lru_key]
    
    def _cleanup_expired(self):
        """Background thread to cleanup expired entries"""
        while True:
            try:
                time.sleep(60)  # Check every minute
                
                with self.lock:
                    now = datetime.now()
                    expired_keys = [
                        key for key, entry in self.cache.items()
                        if now > entry.expires_at
                    ]
                    
                    for key in expired_keys:
                        del self.cache[key]
                        if key in self.access_order:
                            self.access_order.remove(key)
                    
                    self.metrics.cache_size = len(self.cache)
                    
            except Exception as e:
                logging.error(f"Cache cleanup error: {e}")
    
    def get_stats(self) -> Dict:
        """Get cache statistics"""
        with self.lock:
            hit_rate = 0
            if self.metrics.cache_hits + self.metrics.cache_misses > 0:
                hit_rate = self.metrics.cache_hits / (self.metrics.cache_hits + self.metrics.cache_misses)
            
            total_size = sum(entry.size_bytes for entry in self.cache.values())
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hit_rate': hit_rate,
                'hits': self.metrics.cache_hits,
                'misses': self.metrics.cache_misses,
                'total_size_bytes': total_size,
                'avg_entry_size': total_size / len(self.cache) if self.cache else 0
            }

class CompressionManager:
    """Data compression manager"""
    
    @staticmethod
    def compress_data(data: bytes, level: int = 6) -> bytes:
        """Compress data using gzip"""
        return gzip.compress(data, compresslevel=level)
    
    @staticmethod
    def decompress_data(compressed_data: bytes) -> bytes:
        """Decompress gzip data"""
        return gzip.decompress(compressed_data)
    
    @staticmethod
    def compress_json(data: Dict, level: int = 6) -> bytes:
        """Compress JSON data"""
        import json
        json_str = json.dumps(data, separators=(',', ':'))
        return gzip.compress(json_str.encode('utf-8'), compresslevel=level)
    
    @staticmethod
    def decompress_json(compressed_data: bytes) -> Dict:
        """Decompress JSON data"""
        import json
        json_str = gzip.decompress(compressed_data).decode('utf-8')
        return json.loads(json_str)

class ConnectionPool:
    """Database connection pool manager"""
    
    def __init__(self, create_connection: Callable, max_connections: int = 20):
        self.create_connection = create_connection
        self.max_connections = max_connections
        self.pool = []
        self.active_connections = 0
        self.lock = threading.Lock()
    
    def get_connection(self):
        """Get connection from pool"""
        with self.lock:
            if self.pool:
                return self.pool.pop()
            elif self.active_connections < self.max_connections:
                self.active_connections += 1
                return self.create_connection()
            else:
                # Wait for connection to become available
                time.sleep(0.1)
                return self.get_connection()
    
    def return_connection(self, connection):
        """Return connection to pool"""
        with self.lock:
            if len(self.pool) < self.max_connections:
                self.pool.append(connection)
            else:
                # Close excess connection
                try:
                    connection.close()
                except:
                    pass
                self.active_connections -= 1

class PerformanceOptimizer:
    """Main performance optimization engine"""
    
    def __init__(self):
        self.config = get_production_config() if CONFIG_AVAILABLE else None
        
        # Initialize cache
        cache_size = 1000
        cache_ttl = 300
        
        if self.config:
            cache_size = self.config.performance.cache_max_size
            cache_ttl = self.config.performance.cache_ttl
        
        self.cache = InMemoryCache(max_size=cache_size, default_ttl=cache_ttl)
        
        # Initialize thread pool
        thread_pool_size = 10
        if self.config:
            thread_pool_size = self.config.performance.thread_pool_size
        
        self.thread_pool = ThreadPoolExecutor(max_workers=thread_pool_size)
        
        # Performance metrics
        self.metrics = PerformanceMetrics()
        self.request_times = []
        self.metrics_lock = threading.Lock()
        
        # Compression manager
        self.compression = CompressionManager()
        
        print(f"✅ Performance optimizer initialized (cache: {cache_size}, threads: {thread_pool_size})")
    
    def cached(self, ttl: int = None, key_func: Callable = None):
        """Decorator for caching function results"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Generate cache key
                if key_func:
                    cache_key = key_func(*args, **kwargs)
                else:
                    cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
                
                # Try to get from cache
                cached_result = self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                result = func(*args, **kwargs)
                self.cache.set(cache_key, result, ttl)
                return result
            
            return wrapper
        return decorator
    
    def async_execute(self, func: Callable, *args, **kwargs):
        """Execute function asynchronously"""
        return self.thread_pool.submit(func, *args, **kwargs)
    
    def batch_process(self, items: List[Any], process_func: Callable, batch_size: int = None) -> List[Any]:
        """Process items in batches for better performance"""
        if not batch_size:
            batch_size = self.config.performance.batch_size if self.config else 100
        
        results = []
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            batch_results = process_func(batch)
            results.extend(batch_results)
        
        return results
    
    def compress_response(self, data: Dict) -> bytes:
        """Compress API response data"""
        if self.config and self.config.performance.compression_enabled:
            return self.compression.compress_json(data)
        else:
            import json
            return json.dumps(data).encode('utf-8')
    
    def decompress_request(self, compressed_data: bytes) -> Dict:
        """Decompress API request data"""
        if self.config and self.config.performance.compression_enabled:
            return self.compression.decompress_json(compressed_data)
        else:
            import json
            return json.loads(compressed_data.decode('utf-8'))
    
    def track_request_time(self, start_time: float):
        """Track request processing time"""
        with self.metrics_lock:
            request_time = time.time() - start_time
            self.request_times.append(request_time)
            
            # Keep only last 1000 request times
            if len(self.request_times) > 1000:
                self.request_times = self.request_times[-1000:]
            
            # Update metrics
            self.metrics.avg_response_time = sum(self.request_times) / len(self.request_times)
            self.metrics.total_requests += 1
    
    def optimize_query(self, query: str, params: tuple = None) -> tuple:
        """Optimize database query"""
        # Add query optimization logic here
        # For now, just return as-is
        return query, params
    
    def get_performance_stats(self) -> Dict:
        """Get comprehensive performance statistics"""
        cache_stats = self.cache.get_stats()
        
        with self.metrics_lock:
            stats = {
                'cache': cache_stats,
                'requests': {
                    'total': self.metrics.total_requests,
                    'avg_response_time': self.metrics.avg_response_time,
                    'recent_response_times': self.request_times[-10:] if self.request_times else []
                },
                'threads': {
                    'pool_size': self.thread_pool._max_workers,
                    'active_threads': len(self.thread_pool._threads)
                },
                'compression': {
                    'enabled': self.config.performance.compression_enabled if self.config else False,
                    'ratio': self.metrics.compression_ratio
                }
            }
        
        return stats
    
    def optimize_database_connection(self, db_config: Dict) -> Dict:
        """Optimize database connection parameters"""
        optimized_config = db_config.copy()
        
        if self.config:
            # Apply performance optimizations
            optimized_config.update({
                'pool_size': self.config.performance.connection_pool_size,
                'max_overflow': self.config.performance.connection_pool_size // 2,
                'pool_timeout': self.config.performance.query_timeout,
                'pool_recycle': 3600,  # Recycle connections every hour
                'pool_pre_ping': True,  # Validate connections
            })
        
        return optimized_config
    
    def memory_efficient_processing(self, data_generator, process_func: Callable):
        """Process large datasets memory-efficiently using generators"""
        for chunk in data_generator:
            yield process_func(chunk)
    
    def profile_function(self, func: Callable):
        """Decorator to profile function performance"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            
            execution_time = end_time - start_time
            logging.info(f"Function {func.__name__} executed in {execution_time:.4f} seconds")
            
            return result
        return wrapper
    
    def cleanup_resources(self):
        """Cleanup resources on shutdown"""
        try:
            self.thread_pool.shutdown(wait=True)
            self.cache.clear()
            print("✅ Performance optimizer resources cleaned up")
        except Exception as e:
            logging.error(f"Error cleaning up performance optimizer: {e}")

# Decorators for easy use
def cached_result(ttl: int = 300):
    """Decorator for caching function results"""
    optimizer = get_performance_optimizer()
    return optimizer.cached(ttl=ttl)

def async_task(func):
    """Decorator for async task execution"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        optimizer = get_performance_optimizer()
        return optimizer.async_execute(func, *args, **kwargs)
    return wrapper

def profile_performance(func):
    """Decorator for performance profiling"""
    optimizer = get_performance_optimizer()
    return optimizer.profile_function(func)

# Global performance optimizer instance
performance_optimizer = None

def get_performance_optimizer():
    """Get global performance optimizer instance"""
    global performance_optimizer
    if performance_optimizer is None:
        performance_optimizer = PerformanceOptimizer()
    return performance_optimizer