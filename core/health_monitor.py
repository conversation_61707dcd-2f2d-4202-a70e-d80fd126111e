#!/usr/bin/env python3.12
"""
System Health Monitor - Phase 5
Self-monitoring and health check system for the monitoring platform
"""

import os
import sys
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from config.production_config import get_production_config
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False

try:
    from database.db_models import DatabaseOperations
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False

@dataclass
class HealthStatus:
    """Health status for a component"""
    component: str
    status: str  # healthy, warning, critical, unknown
    message: str
    last_check: datetime = None
    response_time: float = 0.0
    details: Dict[str, Any] = None

@dataclass
class SystemHealth:
    """Overall system health"""
    overall_status: str
    components: List[HealthStatus]
    uptime: float
    last_updated: datetime
    performance_score: float = 100.0

class HealthMonitor:
    """System health monitoring and self-diagnostics"""
    
    def __init__(self):
        self.config = get_production_config() if CONFIG_AVAILABLE else None
        self.db_ops = None
        
        if DATABASE_AVAILABLE:
            try:
                self.db_ops = DatabaseOperations()
            except Exception as e:
                logging.warning(f"Database not available for health monitoring: {e}")
        
        self.running = False
        self.check_interval = 30  # seconds
        self.health_history = []
        self.max_history = 1000
        self.start_time = datetime.now()
        
        if self.config:
            self.check_interval = self.config.monitoring.health_check_interval
        
        # Component health checkers
        self.health_checkers = {
            'database': self._check_database_health,
            'memory': self._check_memory_health,
            'disk': self._check_disk_health,
            'cpu': self._check_cpu_health,
            'network': self._check_network_health,
            'processes': self._check_process_health,
            'cache': self._check_cache_health,
            'alerts': self._check_alert_system_health
        }
        
        # Health thresholds
        self.thresholds = {
            'memory_warning': 80.0,
            'memory_critical': 95.0,
            'disk_warning': 85.0,
            'disk_critical': 95.0,
            'cpu_warning': 80.0,
            'cpu_critical': 95.0,
            'response_time_warning': 1.0,
            'response_time_critical': 5.0
        }
        
        print("✅ Health monitor initialized")
    
    def start_monitoring(self):
        """Start health monitoring"""
        if self.running:
            return
        
        self.running = True
        monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        monitor_thread.start()
        print(f"🏥 Health monitoring started (interval: {self.check_interval}s)")
    
    def stop_monitoring(self):
        """Stop health monitoring"""
        self.running = False
        print("🛑 Health monitoring stopped")
    
    def _monitoring_loop(self):
        """Main health monitoring loop"""
        while self.running:
            try:
                health_status = self.check_system_health()
                
                # Store in history
                self.health_history.append(health_status)
                if len(self.health_history) > self.max_history:
                    self.health_history = self.health_history[-self.max_history:]
                
                # Log critical issues
                if health_status.overall_status == 'critical':
                    logging.critical(f"System health critical: {len([c for c in health_status.components if c.status == 'critical'])} critical components")
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                logging.error(f"Health monitoring loop error: {e}")
                time.sleep(10)
    
    def check_system_health(self) -> SystemHealth:
        """Check overall system health"""
        components = []
        critical_count = 0
        warning_count = 0
        
        # Check each component
        for component_name, checker_func in self.health_checkers.items():
            try:
                start_time = time.time()
                health_status = checker_func()
                response_time = time.time() - start_time
                
                health_status.response_time = response_time
                health_status.last_check = datetime.now()
                
                components.append(health_status)
                
                if health_status.status == 'critical':
                    critical_count += 1
                elif health_status.status == 'warning':
                    warning_count += 1
                    
            except Exception as e:
                # Component check failed
                components.append(HealthStatus(
                    component=component_name,
                    status='critical',
                    message=f"Health check failed: {str(e)}",
                    last_check=datetime.now()
                ))
                critical_count += 1
        
        # Determine overall status
        if critical_count > 0:
            overall_status = 'critical'
        elif warning_count > 0:
            overall_status = 'warning'
        else:
            overall_status = 'healthy'
        
        # Calculate performance score
        performance_score = self._calculate_performance_score(components)
        
        # Calculate uptime
        uptime = (datetime.now() - self.start_time).total_seconds()
        
        return SystemHealth(
            overall_status=overall_status,
            components=components,
            uptime=uptime,
            last_updated=datetime.now(),
            performance_score=performance_score
        )
    
    def _check_database_health(self) -> HealthStatus:
        """Check database health"""
        if not self.db_ops:
            return HealthStatus(
                component='database',
                status='unknown',
                message='Database operations not available'
            )
        
        try:
            # Test database connection
            start_time = time.time()
            servers = self.db_ops.get_all_servers()
            query_time = time.time() - start_time
            
            # Check query performance
            if query_time > self.thresholds['response_time_critical']:
                status = 'critical'
                message = f'Database query slow: {query_time:.2f}s'
            elif query_time > self.thresholds['response_time_warning']:
                status = 'warning'
                message = f'Database query performance degraded: {query_time:.2f}s'
            else:
                status = 'healthy'
                message = f'Database operational (query time: {query_time:.3f}s)'
            
            return HealthStatus(
                component='database',
                status=status,
                message=message,
                details={
                    'query_time': query_time,
                    'server_count': len(servers) if servers else 0
                }
            )
            
        except Exception as e:
            return HealthStatus(
                component='database',
                status='critical',
                message=f'Database connection failed: {str(e)}'
            )
    
    def _check_memory_health(self) -> HealthStatus:
        """Check memory health"""
        try:
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            if memory_percent >= self.thresholds['memory_critical']:
                status = 'critical'
                message = f'Critical memory usage: {memory_percent:.1f}%'
            elif memory_percent >= self.thresholds['memory_warning']:
                status = 'warning'
                message = f'High memory usage: {memory_percent:.1f}%'
            else:
                status = 'healthy'
                message = f'Memory usage normal: {memory_percent:.1f}%'
            
            return HealthStatus(
                component='memory',
                status=status,
                message=message,
                details={
                    'percent': memory_percent,
                    'total_gb': memory.total / (1024**3),
                    'available_gb': memory.available / (1024**3),
                    'used_gb': memory.used / (1024**3)
                }
            )
            
        except Exception as e:
            return HealthStatus(
                component='memory',
                status='critical',
                message=f'Memory check failed: {str(e)}'
            )
    
    def _check_disk_health(self) -> HealthStatus:
        """Check disk health"""
        try:
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            if disk_percent >= self.thresholds['disk_critical']:
                status = 'critical'
                message = f'Critical disk usage: {disk_percent:.1f}%'
            elif disk_percent >= self.thresholds['disk_warning']:
                status = 'warning'
                message = f'High disk usage: {disk_percent:.1f}%'
            else:
                status = 'healthy'
                message = f'Disk usage normal: {disk_percent:.1f}%'
            
            return HealthStatus(
                component='disk',
                status=status,
                message=message,
                details={
                    'percent': disk_percent,
                    'total_gb': disk.total / (1024**3),
                    'free_gb': disk.free / (1024**3),
                    'used_gb': disk.used / (1024**3)
                }
            )
            
        except Exception as e:
            return HealthStatus(
                component='disk',
                status='critical',
                message=f'Disk check failed: {str(e)}'
            )
    
    def _check_cpu_health(self) -> HealthStatus:
        """Check CPU health"""
        try:
            # Get CPU usage over 1 second
            cpu_percent = psutil.cpu_percent(interval=1)
            load_avg = psutil.getloadavg()[0] if hasattr(psutil, 'getloadavg') else 0
            
            if cpu_percent >= self.thresholds['cpu_critical']:
                status = 'critical'
                message = f'Critical CPU usage: {cpu_percent:.1f}%'
            elif cpu_percent >= self.thresholds['cpu_warning']:
                status = 'warning'
                message = f'High CPU usage: {cpu_percent:.1f}%'
            else:
                status = 'healthy'
                message = f'CPU usage normal: {cpu_percent:.1f}%'
            
            return HealthStatus(
                component='cpu',
                status=status,
                message=message,
                details={
                    'percent': cpu_percent,
                    'load_avg': load_avg,
                    'core_count': psutil.cpu_count()
                }
            )
            
        except Exception as e:
            return HealthStatus(
                component='cpu',
                status='critical',
                message=f'CPU check failed: {str(e)}'
            )
    
    def _check_network_health(self) -> HealthStatus:
        """Check network health"""
        try:
            # Get network I/O stats
            net_io = psutil.net_io_counters()
            
            # Check for network interfaces
            interfaces = psutil.net_if_addrs()
            active_interfaces = len([name for name, addrs in interfaces.items() if addrs])
            
            if active_interfaces == 0:
                status = 'critical'
                message = 'No active network interfaces'
            else:
                status = 'healthy'
                message = f'Network operational ({active_interfaces} interfaces)'
            
            return HealthStatus(
                component='network',
                status=status,
                message=message,
                details={
                    'interfaces': active_interfaces,
                    'bytes_sent': net_io.bytes_sent,
                    'bytes_recv': net_io.bytes_recv,
                    'packets_sent': net_io.packets_sent,
                    'packets_recv': net_io.packets_recv
                }
            )
            
        except Exception as e:
            return HealthStatus(
                component='network',
                status='warning',
                message=f'Network check incomplete: {str(e)}'
            )
    
    def _check_process_health(self) -> HealthStatus:
        """Check process health"""
        try:
            # Get current process info
            current_process = psutil.Process()
            
            # Check memory usage of current process
            memory_info = current_process.memory_info()
            memory_mb = memory_info.rss / (1024 * 1024)
            
            # Check CPU usage of current process
            cpu_percent = current_process.cpu_percent()
            
            # Check thread count
            thread_count = current_process.num_threads()
            
            if memory_mb > 1000:  # More than 1GB
                status = 'warning'
                message = f'High process memory usage: {memory_mb:.1f}MB'
            elif thread_count > 50:
                status = 'warning'
                message = f'High thread count: {thread_count}'
            else:
                status = 'healthy'
                message = f'Process health normal (Memory: {memory_mb:.1f}MB, Threads: {thread_count})'
            
            return HealthStatus(
                component='processes',
                status=status,
                message=message,
                details={
                    'memory_mb': memory_mb,
                    'cpu_percent': cpu_percent,
                    'thread_count': thread_count,
                    'pid': current_process.pid
                }
            )
            
        except Exception as e:
            return HealthStatus(
                component='processes',
                status='warning',
                message=f'Process check failed: {str(e)}'
            )
    
    def _check_cache_health(self) -> HealthStatus:
        """Check cache system health"""
        try:
            # This would check the performance optimizer cache
            # For now, return healthy
            return HealthStatus(
                component='cache',
                status='healthy',
                message='Cache system operational'
            )
            
        except Exception as e:
            return HealthStatus(
                component='cache',
                status='warning',
                message=f'Cache check failed: {str(e)}'
            )
    
    def _check_alert_system_health(self) -> HealthStatus:
        """Check alert system health"""
        try:
            # This would check the alert engine
            # For now, return healthy
            return HealthStatus(
                component='alerts',
                status='healthy',
                message='Alert system operational'
            )
            
        except Exception as e:
            return HealthStatus(
                component='alerts',
                status='warning',
                message=f'Alert system check failed: {str(e)}'
            )
    
    def _calculate_performance_score(self, components: List[HealthStatus]) -> float:
        """Calculate overall performance score (0-100)"""
        if not components:
            return 0.0
        
        total_score = 0
        for component in components:
            if component.status == 'healthy':
                score = 100
            elif component.status == 'warning':
                score = 70
            elif component.status == 'critical':
                score = 30
            else:  # unknown
                score = 50
            
            # Adjust score based on response time
            if component.response_time > self.thresholds['response_time_critical']:
                score *= 0.5
            elif component.response_time > self.thresholds['response_time_warning']:
                score *= 0.8
            
            total_score += score
        
        return total_score / len(components)
    
    def get_health_summary(self) -> Dict:
        """Get health summary"""
        current_health = self.check_system_health()
        
        # Get recent health history
        recent_history = self.health_history[-10:] if self.health_history else []
        
        # Calculate availability
        if self.health_history:
            healthy_checks = sum(1 for h in self.health_history if h.overall_status == 'healthy')
            availability = (healthy_checks / len(self.health_history)) * 100
        else:
            availability = 100.0
        
        return {
            'current_status': current_health.overall_status,
            'performance_score': current_health.performance_score,
            'uptime_seconds': current_health.uptime,
            'uptime_formatted': self._format_uptime(current_health.uptime),
            'availability_percent': availability,
            'components': [
                {
                    'name': comp.component,
                    'status': comp.status,
                    'message': comp.message,
                    'response_time': comp.response_time,
                    'details': comp.details
                }
                for comp in current_health.components
            ],
            'recent_checks': len(self.health_history),
            'last_updated': current_health.last_updated.isoformat()
        }
    
    def _format_uptime(self, uptime_seconds: float) -> str:
        """Format uptime in human-readable format"""
        uptime = timedelta(seconds=int(uptime_seconds))
        days = uptime.days
        hours, remainder = divmod(uptime.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if days > 0:
            return f"{days}d {hours}h {minutes}m"
        elif hours > 0:
            return f"{hours}h {minutes}m"
        else:
            return f"{minutes}m {seconds}s"
    
    def get_health_trends(self, hours: int = 24) -> Dict:
        """Get health trends over time"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # Filter history by time
        recent_history = [
            h for h in self.health_history
            if h.last_updated > cutoff_time
        ]
        
        if not recent_history:
            return {'error': 'No health data available for the specified period'}
        
        # Calculate trends
        status_counts = {'healthy': 0, 'warning': 0, 'critical': 0, 'unknown': 0}
        performance_scores = []
        
        for health in recent_history:
            status_counts[health.overall_status] += 1
            performance_scores.append(health.performance_score)
        
        avg_performance = sum(performance_scores) / len(performance_scores)
        
        return {
            'period_hours': hours,
            'total_checks': len(recent_history),
            'status_distribution': status_counts,
            'average_performance_score': avg_performance,
            'performance_trend': performance_scores[-10:],  # Last 10 scores
            'availability_percent': (status_counts['healthy'] / len(recent_history)) * 100
        }

# Global health monitor instance
health_monitor = None

def get_health_monitor():
    """Get global health monitor instance"""
    global health_monitor
    if health_monitor is None:
        health_monitor = HealthMonitor()
    return health_monitor