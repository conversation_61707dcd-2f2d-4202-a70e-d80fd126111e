#!/usr/bin/env python3.12
"""
Advanced Alert Engine - Phase 4
Intelligent threshold-based alerting with predictive capabilities
"""

import os
import sys
import time
import json
import threading
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from database.db_models import DatabaseOperations, Alert, ServerEvent, AlertSeverity
    DATABASE_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Database modules not available: {e}")
    DATABASE_AVAILABLE = False

class AlertType(Enum):
    """Alert types"""
    CPU_HIGH = "cpu_high"
    MEMORY_HIGH = "memory_high"
    DISK_HIGH = "disk_high"
    LOAD_HIGH = "load_high"
    SERVER_DOWN = "server_down"
    PERFORMANCE_DEGRADATION = "performance_degradation"
    RESOURCE_EXHAUSTION_PREDICTED = "resource_exhaustion_predicted"
    ANOMALY_DETECTED = "anomaly_detected"

class AlertCondition(Enum):
    """Alert condition types"""
    THRESHOLD = "threshold"
    TREND = "trend"
    ANOMALY = "anomaly"
    PREDICTION = "prediction"

@dataclass
class AlertRule:
    """Alert rule configuration"""
    rule_id: str
    name: str
    alert_type: str
    condition_type: str
    metric: str  # cpu_percent, memory_percent, disk_percent, etc.
    threshold: float
    duration_minutes: int = 5  # How long condition must persist
    severity: str = AlertSeverity.MEDIUM.value
    enabled: bool = True
    servers: List[str] = None  # Specific servers, None = all servers
    created_at: datetime = None
    updated_at: datetime = None

@dataclass
class AlertContext:
    """Context information for an alert"""
    server_ip: str
    server_hostname: str
    metric_name: str
    current_value: float
    threshold_value: float
    duration_minutes: int
    historical_values: List[float]
    trend_direction: str  # "increasing", "decreasing", "stable"
    severity_score: float  # 0-100

class AdvancedAlertEngine:
    """Advanced alert engine with predictive capabilities"""
    
    def __init__(self):
        self.db_ops = None
        self.alert_rules = {}
        self.active_conditions = {}  # Track ongoing conditions
        self.alert_history = {}
        self.running = False
        self.check_interval = 60  # Check every minute
        self.baseline_data = {}  # Store performance baselines
        
        # Initialize database operations
        if DATABASE_AVAILABLE:
            try:
                self.db_ops = DatabaseOperations()
                print("✅ Advanced alert engine database operations initialized")
            except Exception as e:
                print(f"❌ Failed to initialize alert engine database operations: {e}")
                self.db_ops = None
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Load default alert rules
        self._load_default_rules()
    
    def _load_default_rules(self):
        """Load default alert rules"""
        default_rules = [
            AlertRule(
                rule_id="cpu_high_critical",
                name="High CPU Usage - Critical",
                alert_type=AlertType.CPU_HIGH.value,
                condition_type=AlertCondition.THRESHOLD.value,
                metric="cpu_percent",
                threshold=90.0,
                duration_minutes=3,
                severity=AlertSeverity.CRITICAL.value
            ),
            AlertRule(
                rule_id="cpu_high_warning",
                name="High CPU Usage - Warning",
                alert_type=AlertType.CPU_HIGH.value,
                condition_type=AlertCondition.THRESHOLD.value,
                metric="cpu_percent",
                threshold=75.0,
                duration_minutes=5,
                severity=AlertSeverity.HIGH.value
            ),
            AlertRule(
                rule_id="memory_high_critical",
                name="High Memory Usage - Critical",
                alert_type=AlertType.MEMORY_HIGH.value,
                condition_type=AlertCondition.THRESHOLD.value,
                metric="memory_percent",
                threshold=95.0,
                duration_minutes=2,
                severity=AlertSeverity.CRITICAL.value
            ),
            AlertRule(
                rule_id="memory_high_warning",
                name="High Memory Usage - Warning",
                alert_type=AlertType.MEMORY_HIGH.value,
                condition_type=AlertCondition.THRESHOLD.value,
                metric="memory_percent",
                threshold=85.0,
                duration_minutes=5,
                severity=AlertSeverity.HIGH.value
            ),
            AlertRule(
                rule_id="disk_high_critical",
                name="High Disk Usage - Critical",
                alert_type=AlertType.DISK_HIGH.value,
                condition_type=AlertCondition.THRESHOLD.value,
                metric="disk_percent",
                threshold=95.0,
                duration_minutes=1,
                severity=AlertSeverity.CRITICAL.value
            ),
            AlertRule(
                rule_id="disk_high_warning",
                name="High Disk Usage - Warning",
                alert_type=AlertType.DISK_HIGH.value,
                condition_type=AlertCondition.THRESHOLD.value,
                metric="disk_percent",
                threshold=80.0,
                duration_minutes=10,
                severity=AlertSeverity.MEDIUM.value
            ),
            AlertRule(
                rule_id="load_high_critical",
                name="High System Load - Critical",
                alert_type=AlertType.LOAD_HIGH.value,
                condition_type=AlertCondition.THRESHOLD.value,
                metric="load_avg_1",
                threshold=8.0,
                duration_minutes=3,
                severity=AlertSeverity.CRITICAL.value
            )
        ]
        
        for rule in default_rules:
            self.alert_rules[rule.rule_id] = rule
        
        print(f"✅ Loaded {len(default_rules)} default alert rules")
    
    def start_monitoring(self):
        """Start alert monitoring"""
        if not self.db_ops:
            print("❌ Cannot start alert monitoring: Database not available")
            return False
        
        self.running = True
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        monitor_thread.start()
        
        print(f"🚨 Advanced alert monitoring started (check interval: {self.check_interval}s)")
        return True
    
    def stop_monitoring(self):
        """Stop alert monitoring"""
        self.running = False
        print("🛑 Advanced alert monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                # Get all servers
                servers = self.db_ops.get_all_servers()
                
                for server in servers:
                    self._check_server_alerts(server.server_ip, server.hostname)
                
                # Update baselines periodically
                self._update_baselines()
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"Alert monitoring loop error: {e}")
                time.sleep(10)  # Short delay before retrying
    
    def _check_server_alerts(self, server_ip: str, hostname: str):
        """Check alerts for a specific server"""
        try:
            # Get recent metrics for this server
            recent_metrics = self._get_recent_metrics(server_ip, 30)  # Last 30 minutes
            
            if not recent_metrics:
                # Check if server is down
                self._check_server_down(server_ip, hostname)
                return
            
            # Check each alert rule
            for rule_id, rule in self.alert_rules.items():
                if not rule.enabled:
                    continue
                
                # Check if rule applies to this server
                if rule.servers and server_ip not in rule.servers:
                    continue
                
                self._evaluate_alert_rule(rule, server_ip, hostname, recent_metrics)
                
        except Exception as e:
            self.logger.error(f"Error checking alerts for server {server_ip}: {e}")
    
    def _get_recent_metrics(self, server_ip: str, minutes: int) -> List[Dict]:
        """Get recent metrics for a server"""
        try:
            metrics = self.db_ops.get_system_metrics_history(server_ip, hours=minutes/60)
            
            # Convert to dict format for easier processing
            metrics_data = []
            for metric in metrics:
                metrics_data.append({
                    'timestamp': metric.timestamp,
                    'cpu_percent': metric.cpu_percent or 0,
                    'memory_percent': metric.memory_percent or 0,
                    'disk_percent': metric.disk_percent or 0,
                    'load_avg_1': metric.load_avg_1 or 0,
                    'server_hostname': metric.server_hostname
                })
            
            return metrics_data
            
        except Exception as e:
            self.logger.error(f"Error getting recent metrics for {server_ip}: {e}")
            return []
    
    def _evaluate_alert_rule(self, rule: AlertRule, server_ip: str, hostname: str, metrics: List[Dict]):
        """Evaluate an alert rule against server metrics"""
        try:
            if not metrics:
                return
            
            # Get metric values for the rule
            metric_values = [m.get(rule.metric, 0) for m in metrics if m.get(rule.metric) is not None]
            
            if not metric_values:
                return
            
            current_value = metric_values[0]  # Most recent value
            
            # Check threshold condition
            if rule.condition_type == AlertCondition.THRESHOLD.value:
                self._check_threshold_condition(rule, server_ip, hostname, current_value, metric_values)
            elif rule.condition_type == AlertCondition.TREND.value:
                self._check_trend_condition(rule, server_ip, hostname, current_value, metric_values)
            elif rule.condition_type == AlertCondition.ANOMALY.value:
                self._check_anomaly_condition(rule, server_ip, hostname, current_value, metric_values)
                
        except Exception as e:
            self.logger.error(f"Error evaluating alert rule {rule.rule_id}: {e}")
    
    def _check_threshold_condition(self, rule: AlertRule, server_ip: str, hostname: str, 
                                 current_value: float, metric_values: List[float]):
        """Check threshold-based alert condition"""
        condition_key = f"{rule.rule_id}_{server_ip}"
        
        # Check if threshold is exceeded
        if current_value >= rule.threshold:
            # Start or continue tracking this condition
            if condition_key not in self.active_conditions:
                self.active_conditions[condition_key] = {
                    'start_time': datetime.now(),
                    'rule': rule,
                    'server_ip': server_ip,
                    'hostname': hostname,
                    'values': [current_value]
                }
            else:
                self.active_conditions[condition_key]['values'].append(current_value)
            
            # Check if condition has persisted long enough
            condition = self.active_conditions[condition_key]
            duration = datetime.now() - condition['start_time']
            
            if duration.total_seconds() >= rule.duration_minutes * 60:
                # Trigger alert
                self._trigger_alert(rule, server_ip, hostname, current_value, metric_values)
                
                # Reset condition to avoid duplicate alerts
                del self.active_conditions[condition_key]
        else:
            # Condition no longer met, clear it
            if condition_key in self.active_conditions:
                del self.active_conditions[condition_key]
    
    def _check_trend_condition(self, rule: AlertRule, server_ip: str, hostname: str,
                             current_value: float, metric_values: List[float]):
        """Check trend-based alert condition"""
        if len(metric_values) < 5:  # Need at least 5 data points
            return
        
        # Calculate trend
        recent_values = metric_values[:10]  # Last 10 values
        trend_slope = self._calculate_trend_slope(recent_values)
        
        # Check for concerning trends
        if trend_slope > 2.0 and current_value > rule.threshold * 0.8:  # Rapidly increasing and approaching threshold
            context = AlertContext(
                server_ip=server_ip,
                server_hostname=hostname,
                metric_name=rule.metric,
                current_value=current_value,
                threshold_value=rule.threshold,
                duration_minutes=rule.duration_minutes,
                historical_values=recent_values,
                trend_direction="increasing",
                severity_score=min(100, (current_value / rule.threshold) * 100)
            )
            
            self._trigger_trend_alert(rule, context)
    
    def _check_anomaly_condition(self, rule: AlertRule, server_ip: str, hostname: str,
                               current_value: float, metric_values: List[float]):
        """Check anomaly-based alert condition"""
        if len(metric_values) < 20:  # Need sufficient historical data
            return
        
        # Calculate baseline statistics
        baseline_values = metric_values[5:]  # Exclude recent values for baseline
        if not baseline_values:
            return
        
        baseline_mean = statistics.mean(baseline_values)
        baseline_stdev = statistics.stdev(baseline_values) if len(baseline_values) > 1 else 0
        
        # Check for anomaly (value significantly different from baseline)
        if baseline_stdev > 0:
            z_score = abs(current_value - baseline_mean) / baseline_stdev
            
            if z_score > 3.0:  # 3 standard deviations from mean
                context = AlertContext(
                    server_ip=server_ip,
                    server_hostname=hostname,
                    metric_name=rule.metric,
                    current_value=current_value,
                    threshold_value=baseline_mean,
                    duration_minutes=rule.duration_minutes,
                    historical_values=baseline_values[-10:],
                    trend_direction="anomaly",
                    severity_score=min(100, z_score * 20)
                )
                
                self._trigger_anomaly_alert(rule, context)
    
    def _trigger_alert(self, rule: AlertRule, server_ip: str, hostname: str, 
                      current_value: float, metric_values: List[float]):
        """Trigger a threshold-based alert"""
        try:
            # Create alert message
            message = f"{rule.name}: {rule.metric} is {current_value:.1f}% (threshold: {rule.threshold}%)"
            
            # Create alert object
            alert = Alert(
                server_ip=server_ip,
                alert_type=rule.alert_type,
                alert_message=message,
                severity=rule.severity,
                threshold_value=rule.threshold,
                current_value=current_value
            )
            
            # Store alert in database
            success = self.db_ops.create_alert(alert)
            
            if success:
                print(f"🚨 ALERT TRIGGERED: {message} on {hostname} ({server_ip})")
                
                # Log server event
                event = ServerEvent(
                    server_ip=server_ip,
                    event_type="alert",
                    message=message,
                    severity=rule.severity,
                    details={
                        'rule_id': rule.rule_id,
                        'metric': rule.metric,
                        'current_value': current_value,
                        'threshold': rule.threshold,
                        'alert_type': rule.alert_type
                    }
                )
                self.db_ops.log_server_event(event)
                
                # Store in alert history
                alert_key = f"{server_ip}_{rule.rule_id}"
                self.alert_history[alert_key] = {
                    'timestamp': datetime.now(),
                    'alert': alert,
                    'rule': rule
                }
            
        except Exception as e:
            self.logger.error(f"Error triggering alert: {e}")
    
    def _trigger_trend_alert(self, rule: AlertRule, context: AlertContext):
        """Trigger a trend-based alert"""
        try:
            message = f"Trend Alert: {context.metric_name} trending upward on {context.server_hostname} " \
                     f"(current: {context.current_value:.1f}%, approaching threshold: {context.threshold_value:.1f}%)"
            
            alert = Alert(
                server_ip=context.server_ip,
                alert_type=AlertType.PERFORMANCE_DEGRADATION.value,
                alert_message=message,
                severity=AlertSeverity.MEDIUM.value,
                threshold_value=context.threshold_value,
                current_value=context.current_value
            )
            
            success = self.db_ops.create_alert(alert)
            if success:
                print(f"📈 TREND ALERT: {message}")
                
        except Exception as e:
            self.logger.error(f"Error triggering trend alert: {e}")
    
    def _trigger_anomaly_alert(self, rule: AlertRule, context: AlertContext):
        """Trigger an anomaly-based alert"""
        try:
            message = f"Anomaly Detected: {context.metric_name} on {context.server_hostname} " \
                     f"(current: {context.current_value:.1f}%, baseline: {context.threshold_value:.1f}%)"
            
            alert = Alert(
                server_ip=context.server_ip,
                alert_type=AlertType.ANOMALY_DETECTED.value,
                alert_message=message,
                severity=AlertSeverity.HIGH.value,
                threshold_value=context.threshold_value,
                current_value=context.current_value
            )
            
            success = self.db_ops.create_alert(alert)
            if success:
                print(f"🔍 ANOMALY ALERT: {message}")
                
        except Exception as e:
            self.logger.error(f"Error triggering anomaly alert: {e}")
    
    def _check_server_down(self, server_ip: str, hostname: str):
        """Check if server appears to be down"""
        try:
            # Get latest metrics
            latest_metrics = self.db_ops.get_latest_system_metrics(server_ip)
            
            if latest_metrics:
                # Check how old the latest data is
                age = datetime.now() - latest_metrics.timestamp
                
                if age.total_seconds() > 300:  # No data for 5 minutes
                    message = f"Server appears down: No data received for {age.total_seconds():.0f} seconds"
                    
                    alert = Alert(
                        server_ip=server_ip,
                        alert_type=AlertType.SERVER_DOWN.value,
                        alert_message=message,
                        severity=AlertSeverity.CRITICAL.value,
                        current_value=age.total_seconds()
                    )
                    
                    success = self.db_ops.create_alert(alert)
                    if success:
                        print(f"🔴 SERVER DOWN ALERT: {message} - {hostname} ({server_ip})")
                        
        except Exception as e:
            self.logger.error(f"Error checking server down status: {e}")
    
    def _calculate_trend_slope(self, values: List[float]) -> float:
        """Calculate trend slope for a series of values"""
        if len(values) < 2:
            return 0.0
        
        # Simple linear regression slope calculation
        n = len(values)
        x_values = list(range(n))
        
        x_mean = sum(x_values) / n
        y_mean = sum(values) / n
        
        numerator = sum((x_values[i] - x_mean) * (values[i] - y_mean) for i in range(n))
        denominator = sum((x_values[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return 0.0
        
        return numerator / denominator
    
    def _update_baselines(self):
        """Update performance baselines for anomaly detection"""
        try:
            servers = self.db_ops.get_all_servers()
            
            for server in servers:
                # Get historical data for baseline calculation
                metrics = self.db_ops.get_system_metrics_history(server.server_ip, 24)  # Last 24 hours
                
                if len(metrics) >= 50:  # Need sufficient data
                    baseline = {
                        'cpu_mean': statistics.mean([m.cpu_percent for m in metrics if m.cpu_percent]),
                        'memory_mean': statistics.mean([m.memory_percent for m in metrics if m.memory_percent]),
                        'disk_mean': statistics.mean([m.disk_percent for m in metrics if m.disk_percent]),
                        'updated_at': datetime.now()
                    }
                    
                    self.baseline_data[server.server_ip] = baseline
                    
        except Exception as e:
            self.logger.error(f"Error updating baselines: {e}")
    
    def get_alert_status(self) -> Dict:
        """Get current alert engine status"""
        return {
            'running': self.running,
            'rules_count': len(self.alert_rules),
            'active_conditions': len(self.active_conditions),
            'check_interval': self.check_interval,
            'baseline_servers': len(self.baseline_data)
        }
    
    def get_recent_alerts(self, hours: int = 24) -> List[Dict]:
        """Get recent alerts"""
        try:
            if not self.db_ops:
                return []
            
            # This would need a method in DatabaseOperations to get recent alerts
            # For now, return from memory
            recent_alerts = []
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            for alert_key, alert_data in self.alert_history.items():
                if alert_data['timestamp'] > cutoff_time:
                    recent_alerts.append({
                        'timestamp': alert_data['timestamp'].isoformat(),
                        'server_ip': alert_data['alert'].server_ip,
                        'alert_type': alert_data['alert'].alert_type,
                        'message': alert_data['alert'].alert_message,
                        'severity': alert_data['alert'].severity,
                        'current_value': alert_data['alert'].current_value,
                        'threshold_value': alert_data['alert'].threshold_value
                    })
            
            return sorted(recent_alerts, key=lambda x: x['timestamp'], reverse=True)
            
        except Exception as e:
            self.logger.error(f"Error getting recent alerts: {e}")
            return []

# Global instance
advanced_alert_engine = None

def get_advanced_alert_engine():
    """Get global advanced alert engine instance"""
    global advanced_alert_engine
    if advanced_alert_engine is None:
        advanced_alert_engine = AdvancedAlertEngine()
    return advanced_alert_engine