#!/usr/bin/env python3.12
"""
Simple Database Cleaner
Removes all data from monitoring tables (without sequence reset)
"""

import os
import sys

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from database.db_models import DatabaseOperations
    DATABASE_AVAILABLE = True
    print("✅ Database modules loaded successfully")
except ImportError as e:
    print(f"❌ Error importing database modules: {e}")
    DATABASE_AVAILABLE = False
    sys.exit(1)

def clear_all_data():
    """Clear all monitoring data from database tables"""
    
    try:
        # Initialize database operations
        db_ops = DatabaseOperations()
        db = db_ops.db
        
        print("🧹 CLEARING ALL MONITORING DATA")
        print("=" * 50)
        
        # Tables to clear (in order to handle foreign key constraints)
        tables_to_clear = [
            'alerts_log',
            'server_events', 
            'disk_metrics',
            'network_metrics',
            'process_metrics',
            'system_metrics',
            'servers'
        ]
        
        # Clear each table
        total_cleared = 0
        for table in tables_to_clear:
            try:
                # Get count before clearing
                count_result = db.execute_query(f"SELECT COUNT(*) FROM {table}")
                count_before = count_result[0][0] if count_result else 0
                
                if count_before > 0:
                    # Clear the table
                    db.execute_query(f"DELETE FROM {table}")
                    print(f"   ✅ {table}: Cleared {count_before} records")
                    total_cleared += count_before
                else:
                    print(f"   ⚪ {table}: Already empty")
                    
            except Exception as e:
                print(f"   ❌ {table}: Error clearing - {e}")
        
        print(f"\n🎉 CLEARING COMPLETE!")
        print(f"✅ Total records deleted: {total_cleared}")
        
        # Verify tables are empty
        print(f"\n🔍 VERIFICATION:")
        all_empty = True
        for table in tables_to_clear:
            try:
                count_result = db.execute_query(f"SELECT COUNT(*) FROM {table}")
                count = count_result[0][0] if count_result else 0
                if count == 0:
                    print(f"   ✅ {table}: Empty")
                else:
                    print(f"   ⚠️  {table}: {count} records remaining")
                    all_empty = False
            except Exception as e:
                print(f"   ❌ {table}: Error verifying - {e}")
                all_empty = False
        
        if all_empty:
            print(f"\n🎯 SUCCESS: All tables are now empty!")
            print(f"🚀 Ready for fresh real-time data collection!")
            return True
        else:
            print(f"\n⚠️  Some records may still remain")
            return False
            
    except Exception as e:
        print(f"❌ Error clearing database: {e}")
        return False

if __name__ == "__main__":
    print("🧹 SIMPLE DATABASE CLEANER")
    print("=" * 50)
    
    success = clear_all_data()
    
    if success:
        print("\n" + "=" * 50)
        print("🎯 DATABASE CLEARED SUCCESSFULLY!")
        print("🚀 Start your dashboard for fresh real-time data:")
        print("   python3.12 web_dashboard.py --port 8005")
        print("=" * 50)
    else:
        print("\n❌ Database clearing may have issues")