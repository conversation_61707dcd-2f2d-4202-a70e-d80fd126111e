#!/usr/bin/env python3.12
"""
Clear Database Script
Removes all data from monitoring tables for fresh start
"""

import os
import sys
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from database.db_connection import get_database
    from database.db_models import DatabaseOperations
    DATABASE_AVAILABLE = True
    print("✅ Database modules loaded successfully")
except ImportError as e:
    print(f"❌ Error importing database modules: {e}")
    DATABASE_AVAILABLE = False
    sys.exit(1)

def clear_all_monitoring_data():
    """Clear all monitoring data from database tables"""
    
    if not DATABASE_AVAILABLE:
        print("❌ Database not available")
        return False
    
    try:
        # Initialize database operations
        db_ops = DatabaseOperations()
        db = db_ops.db
        
        print("🧹 CLEARING ALL MONITORING DATA")
        print("=" * 50)
        
        # List of tables to clear (in order to handle foreign key constraints)
        tables_to_clear = [
            'alerts_log',
            'server_events', 
            'disk_metrics',
            'network_metrics',
            'process_metrics',
            'system_metrics',
            'servers'
        ]
        
        # Get current record counts before clearing
        print("📊 CURRENT DATA COUNTS:")
        total_records_before = 0
        for table in tables_to_clear:
            try:
                count_result = db.execute_query(f"SELECT COUNT(*) FROM {table}")
                count = count_result[0][0] if count_result else 0
                total_records_before += count
                print(f"   {table}: {count} records")
            except Exception as e:
                print(f"   {table}: Error getting count - {e}")
        
        print(f"\n📈 TOTAL RECORDS TO CLEAR: {total_records_before}")
        
        if total_records_before == 0:
            print("✅ Database is already empty!")
            return True
        
        # Confirm clearing
        print(f"\n⚠️  WARNING: This will delete ALL {total_records_before} monitoring records!")
        print("   This action cannot be undone.")
        
        # Auto-confirm for script execution
        confirm = input("\n🤔 Are you sure you want to clear all data? (yes/no): ").lower().strip()
        
        if confirm not in ['yes', 'y']:
            print("❌ Operation cancelled by user")
            return False
        
        print(f"\n🗑️  CLEARING DATA...")
        print("=" * 30)
        
        # Clear each table
        cleared_tables = 0
        total_cleared = 0
        
        for table in tables_to_clear:
            try:
                # Get count before clearing
                count_result = db.execute_query(f"SELECT COUNT(*) FROM {table}")
                count_before = count_result[0][0] if count_result else 0
                
                if count_before > 0:
                    # Clear the table
                    db.execute_query(f"DELETE FROM {table}")
                    
                    # Reset auto-increment counter
                    db.execute_query(f"ALTER SEQUENCE {table}_id_seq RESTART WITH 1")
                    
                    print(f"   ✅ {table}: Cleared {count_before} records")
                    total_cleared += count_before
                    cleared_tables += 1
                else:
                    print(f"   ⚪ {table}: Already empty")
                    
            except Exception as e:
                print(f"   ❌ {table}: Error clearing - {e}")
        
        print(f"\n🎉 CLEARING COMPLETE!")
        print("=" * 30)
        print(f"✅ Tables cleared: {cleared_tables}/{len(tables_to_clear)}")
        print(f"✅ Records deleted: {total_cleared}")
        
        # Verify tables are empty
        print(f"\n🔍 VERIFICATION:")
        total_remaining = 0
        for table in tables_to_clear:
            try:
                count_result = db.execute_query(f"SELECT COUNT(*) FROM {table}")
                count = count_result[0][0] if count_result else 0
                total_remaining += count
                status = "✅ Empty" if count == 0 else f"⚠️  {count} records remaining"
                print(f"   {table}: {status}")
            except Exception as e:
                print(f"   {table}: Error verifying - {e}")
        
        if total_remaining == 0:
            print(f"\n🎯 SUCCESS: All tables are now empty!")
            print(f"🚀 Ready for fresh real-time data collection!")
            return True
        else:
            print(f"\n⚠️  WARNING: {total_remaining} records still remain")
            return False
            
    except Exception as e:
        print(f"❌ Error clearing database: {e}")
        return False

def show_database_status():
    """Show current database status"""
    try:
        db_ops = DatabaseOperations()
        stats = db_ops.get_database_stats()
        
        print("\n📊 CURRENT DATABASE STATUS:")
        print("=" * 40)
        
        total_records = 0
        for table, count in stats.items():
            total_records += count
            icon = "🖥️" if table == "servers" else "📊" if "metrics" in table else "📋"
            print(f"   {icon} {table}: {count} records")
        
        print(f"\n📈 TOTAL RECORDS: {total_records}")
        
        if total_records == 0:
            print("✅ Database is ready for fresh data collection!")
        else:
            print("⚠️  Database contains existing data")
            
    except Exception as e:
        print(f"❌ Error checking database status: {e}")

if __name__ == "__main__":
    print("🧹 DATABASE CLEANER UTILITY")
    print("=" * 50)
    print("This script will clear all monitoring data from your database")
    print("Use this to start fresh with real-time data collection")
    print()
    
    # Show current status
    show_database_status()
    
    # Clear data
    success = clear_all_monitoring_data()
    
    if success:
        print("\n" + "=" * 50)
        print("🎯 DATABASE CLEARED SUCCESSFULLY!")
        print("🚀 You can now start your dashboard for fresh real-time data:")
        print("   python3.12 web_dashboard.py --port 8005")
        print("=" * 50)
    else:
        print("\n❌ Database clearing failed or was cancelled")